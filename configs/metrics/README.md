# Metrics Configuration

This directory contains Hydra configuration files for metrics used in the Audio Flow Matching Lightning project. The metrics system supports both standard metrics and empty configurations for cases where no metrics are needed.

## Configuration Structure

Each metrics config file follows this structure:

```yaml
defaults:
  - metric_fn/multiclass_accuracy  # Include specific metric function configs
  - _self_
```

## Available Metrics

### Individual Metric Functions (`metric_fn/`)

| Config File | Metric Function | Description |
|-------------|-----------------|-------------|
| `multiclass_accuracy.yaml` | Accuracy | Multiclass accuracy metric for classification tasks |

### Complete Metrics Configurations

| Config File | Metrics | Use Case |
|-------------|---------|----------|
| `default.yaml` | MultiClassAccuracy | Standard classification training |
| `none.yaml` | Empty | No metrics needed (e.g., for audio generation tasks) |

## Empty Metrics Configuration

For tasks that don't require metrics during training/validation (such as audio generation with flow matching), use the `none.yaml` configuration:

```yaml
# configs/metrics/none.yaml
# Empty metrics configuration
# Use this when no metrics are needed during training/validation
```

This will result in an empty metrics dictionary being passed to the Lightning module, which handles empty metrics gracefully.

## Usage in Training

In `train.py`, metrics are instantiated using:

```python
metrics = instantiate_metrics(cfg.metrics)
```

This returns a dictionary with metric functions that can be empty:

```python
# With metrics
{
    "MultiClassAccuracy": <torchmetrics.Accuracy instance>
}

# Without metrics (empty)
{}
```

## Example Usage

```bash
# Use default metrics (for classification)
python src/train.py metrics=default

# Use no metrics (for audio generation)
python src/train.py metrics=none

# Override metric parameters
python src/train.py metrics=default metrics.metric_fn.MultiClassAccuracy.num_classes=5
```

## Lightning Module Integration

The Lightning module handles both populated and empty metrics collections:

```python
def _init_metrics(self):
    """Initialize the metric dict."""
    self.train_metric_collect = MetricCollection({}, prefix="train/")
    
    # This works fine even when self.metrics is empty
    for metric_name, metric_fn in self.metrics.items():
        self.train_metric_collect[metric_name] = metric_fn()
    
    # Empty metric collections work normally
    self.val_metric_collect = self.train_metric_collect.clone(prefix="val/")
```

## Adding New Metrics

1. **Create metric config** in `configs/metrics/metric_fn/your_metric.yaml`
2. **Create complete config** in `configs/metrics/your_config.yaml`
3. **Reference in defaults** of the complete config

Example new metric config:

```yaml
# configs/metrics/metric_fn/f1_score.yaml
F1Score:
  _target_: torchmetrics.classification.F1Score
  _partial_: true
  task: "multiclass"
  num_classes: 10
```

# @package _global_

# specify here default configuration
# order of defaults determines the order in which configs override each other
defaults:
  - _self_
  - data: gtzan
  - models: bsroformer_text2music
  - losses: default  # Uses MSE loss for flow matching
  - metrics: none    # No metrics for audio flow matching
  - lit_module: audio_flowmatching
  - callbacks: default
  - loggers: tensorboard
  - trainer: default
  - paths: default
  - extras: default
  - hydra: default

# task name, determines output directory path
task_name: "audio_flowmatching"

# tags to help you identify your experiments
# you can overwrite this in experiment configs
# overwrite from command line with `python train.py tags="[first_tag, second_tag]"`
tags: ["audio", "flow_matching", "text2music", "gtzan"]

# set False to skip model training
train: True

# evaluate on test set, using best model weights achieved during training
# lightning chooses best weights based on the metric specified in checkpoint callback
test: False

# simply provide checkpoint path to resume training
ckpt_path: null

# seed for random number generators in pytorch, numpy and python.random
seed: 42

# Float32 matrix multiplication precision
float32_matmul_precision: "medium"  # "highest" | "high" | "medium"

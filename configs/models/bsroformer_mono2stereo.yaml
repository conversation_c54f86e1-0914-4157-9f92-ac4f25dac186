# BSRoformer model configuration for mono-to-stereo conversion
# This config defines both the tokenizer and BSRoformer model for stereo upmixing

# Tokenizer for mono-to-stereo conversion
tokenizer:
  _target_: src.models.tokenizers.mono2stereo.Mono2StereoTokenizer

# BSRoformer model for mono-to-stereo conversion
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel

  # Condition parameters for mono-to-stereo (mono audio conditioning)
  y_dim: 0 # One-hot label dimension (disabled)
  c_dim: 0 # Global conditioning dimension (disabled)
  ct_dim: 0 # Temporal conditioning dimension (disabled)
  ctf_dim: 128 # Temporal-frequency conditioning (mono mel features)
  cx_dim: 0 # Cross-attention conditioning dimension (disabled)
  in_channels: 2 # Input channels (stereo output)

  # Transformer parameters
  patch_size: [4, 4] # [time_patch, freq_patch]
  n_layer: 12 # Number of transformer layers
  n_head: 12 # Number of attention heads
  n_embd: 384 # Embedding dimension

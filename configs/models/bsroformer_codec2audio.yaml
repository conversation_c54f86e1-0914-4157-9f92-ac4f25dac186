# BSRoformer model configuration for codec-to-audio generation
# This config defines both the tokenizer and BSRoformer model for codec token to audio generation

# Tokenizer for codec-to-audio generation
tokenizer:
  _target_: src.models.tokenizers.codec2audio.Codec2AudioTokenizer
  n_quantizers: 1

# BSRoformer model for codec-to-audio generation
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel
  
  # Condition parameters for codec-to-audio (codec token conditioning)
  y_dim: 0        # One-hot label dimension (disabled)
  c_dim: 0        # Global conditioning dimension (disabled)
  ct_dim: 1024    # Temporal conditioning (codec tokens)
  ctf_dim: 0      # Temporal-frequency conditioning dimension (disabled)
  cx_dim: 0       # Cross-attention conditioning dimension (disabled)
  in_channels: 1  # Input channels (mono audio)
  
  # Transformer parameters
  patch_size: [4, 4]  # [time_patch, freq_patch]
  n_layer: 12         # Number of transformer layers
  n_head: 12          # Number of attention heads
  n_embd: 384         # Embedding dimension

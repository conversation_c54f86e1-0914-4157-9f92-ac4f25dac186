# BSRoformer model configuration for audio super-resolution
# This config defines both the tokenizer and BSRoformer model for audio upsampling

# Tokenizer for super-resolution
tokenizer:
  _target_: src.models.tokenizers.superresolution.SuperResolutionTokenizer
  distorted_sample_rate: 8000

# BSRoformer model for super-resolution
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel

  # Condition parameters for super-resolution (low-quality audio conditioning)
  y_dim: 0 # One-hot label dimension (disabled)
  c_dim: 0 # Global conditioning dimension (disabled)
  ct_dim: 0 # Temporal conditioning dimension (disabled)
  ctf_dim: 128 # Temporal-frequency conditioning (low-quality mel features)
  cx_dim: 0 # Cross-attention conditioning dimension (disabled)
  in_channels: 1 # Input channels (mono audio)

  # Transformer parameters
  patch_size: [4, 4] # [time_patch, freq_patch]
  n_layer: 12 # Number of transformer layers
  n_head: 12 # Number of attention heads
  n_embd: 384 # Embedding dimension

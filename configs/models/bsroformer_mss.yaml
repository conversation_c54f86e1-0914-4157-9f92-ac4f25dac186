# BSRoformer model configuration for music source separation
# This config defines both the tokenizer and BSRoformer model for source separation tasks

# Tokenizer for music source separation
tokenizer:
  _target_: src.models.tokenizers.mss.MSSTokenizer

# BSRoformer model for music source separation
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel

  # Condition parameters for source separation (mixture conditioning)
  y_dim: 4 # Source type labels (vocals, drums, bass, other)
  c_dim: 0 # Global conditioning dimension (disabled)
  ct_dim: 0 # Temporal conditioning dimension (disabled)
  ctf_dim: 128 # Temporal-frequency conditioning (mixture mel features)
  cx_dim: 0 # Cross-attention conditioning dimension (disabled)
  in_channels: 1 # Input channels (mono audio per source)

  # Transformer parameters
  patch_size: [4, 4] # [time_patch, freq_patch]
  n_layer: 12 # Number of transformer layers
  n_head: 12 # Number of attention heads
  n_embd: 384 # Embedding dimension

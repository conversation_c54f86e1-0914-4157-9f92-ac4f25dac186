# BSRoformer model configuration for text-to-music generation
# This config defines both the tokenizer and BSRoformer model for genre-conditioned music generation

# Tokenizer for text-to-music (genre conditioning)
tokenizer:
  _target_: src.models.tokenizers.text2music.Text2MusicTokenizer

# BSRoformer model for text-to-music generation
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel

  # Condition parameters for text-to-music (genre conditioning)
  y_dim: 10 # Number of genre classes (GTZAN has 10 genres)
  c_dim: 0 # Global conditioning dimension (disabled)
  ct_dim: 0 # Temporal conditioning dimension (disabled)
  ctf_dim: 0 # Temporal-frequency conditioning dimension (disabled)
  cx_dim: 0 # Cross-attention conditioning dimension (disabled)
  in_channels: 1 # Input channels (mono audio)

  # Transformer parameters
  patch_size: [4, 4] # [time_patch, freq_patch]
  n_layer: 12 # Number of transformer layers
  n_head: 12 # Number of attention heads
  n_embd: 384 # Embedding dimension

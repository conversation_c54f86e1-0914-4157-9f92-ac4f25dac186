# @package _global_
defaults:
  - tokenizers: default
  - _self_

# BSRoformer model configuration
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel
  
  # Condition parameters
  y_dim: 0        # One-hot label dimension (0 = disabled)
  c_dim: 0        # Global conditioning dimension (0 = disabled)
  ct_dim: 0       # Temporal conditioning dimension (0 = disabled)
  ctf_dim: 0      # Temporal-frequency conditioning dimension (0 = disabled)
  cx_dim: 0       # Cross-attention conditioning dimension (0 = disabled)
  in_channels: 1  # Input channels (1 for mono, 2 for stereo)
  
  # Transformer parameters
  patch_size: [4, 4]  # [time_patch, freq_patch]
  n_layer: 12         # Number of transformer layers
  n_head: 12          # Number of attention heads
  n_embd: 384         # Embedding dimension

# Example configurations for different tasks:

# For text-to-music generation (with genre labels):
# bsroformer:
#   y_dim: 10  # Number of genre classes
#   c_dim: 0
#   ct_dim: 0
#   ctf_dim: 0
#   cx_dim: 0

# For vocal-to-music generation:
# bsroformer:
#   y_dim: 0
#   c_dim: 0
#   ct_dim: 0
#   ctf_dim: 128  # Vocal mel spectrogram features
#   cx_dim: 0

# For codec-to-audio generation:
# bsroformer:
#   y_dim: 0
#   c_dim: 0
#   ct_dim: 1024  # Codec token dimension
#   ctf_dim: 0
#   cx_dim: 0

# For super-resolution:
# bsroformer:
#   y_dim: 0
#   c_dim: 0
#   ct_dim: 0
#   ctf_dim: 128  # Low-quality mel spectrogram features
#   cx_dim: 0

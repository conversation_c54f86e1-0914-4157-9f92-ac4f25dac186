# Default model configuration
# This config defines both the tokenizer and BSRoformer model with default settings

# Default tokenizer
tokenizer:
  _target_: src.models.tokenizers.text2music.Text2MusicTokenizer

# Default BSRoformer model
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel

  # Default condition parameters (all disabled)
  y_dim: 0        # One-hot label dimension (disabled)
  c_dim: 0        # Global conditioning dimension (disabled)
  ct_dim: 0       # Temporal conditioning dimension (disabled)
  ctf_dim: 0      # Temporal-frequency conditioning dimension (disabled)
  cx_dim: 0       # Cross-attention conditioning dimension (disabled)
  in_channels: 1  # Input channels (mono audio)

  # Default transformer parameters
  patch_size: [4, 4]  # [time_patch, freq_patch]
  n_layer: 12         # Number of transformer layers
  n_head: 12          # Number of attention heads
  n_embd: 384         # Embedding dimension

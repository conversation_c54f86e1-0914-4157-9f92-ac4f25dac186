# BSRoformer model configuration for vocal-to-music generation
# This config defines both the tokenizer and BSRoformer model for vocal-conditioned music generation

# Tokenizer for vocal-to-music (vocal conditioning)
tokenizer:
  _target_: src.models.tokenizers.vocal2music.Vocal2MusicTokenizer

# BSRoformer model for vocal-to-music generation
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel

  # Condition parameters for vocal-to-music (vocal mel conditioning)
  y_dim: 0        # One-hot label dimension (disabled)
  c_dim: 0        # Global conditioning dimension (disabled)
  ct_dim: 0       # Temporal conditioning dimension (disabled)
  ctf_dim: 128    # Temporal-frequency conditioning (vocal mel features)
  cx_dim: 0       # Cross-attention conditioning dimension (disabled)
  in_channels: 1  # Input channels (mono audio)

  # Transformer parameters
  patch_size: [4, 4]  # [time_patch, freq_patch]
  n_layer: 12         # Number of transformer layers
  n_head: 12          # Number of attention heads
  n_embd: 384         # Embedding dimension

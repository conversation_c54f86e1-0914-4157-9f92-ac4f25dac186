# BSRoformer Model Configurations

This directory contains Hydra configuration files for the BSRoformer model and associated tokenizers. Each configuration file defines both the tokenizer and the main BSRoformer model, allowing them to be instantiated together via the `instantiate_models()` function in `train.py`.

## Configuration Structure

Each config file follows this structure:

```yaml
# Tokenizer configuration
tokenizer:
  _target_: src.models.tokenizers.{task}.{TaskTokenizer}
  # tokenizer-specific parameters

# BSRoformer model configuration
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel
  # conditioning parameters
  y_dim: 0        # One-hot label dimension
  c_dim: 0        # Global conditioning dimension
  ct_dim: 0       # Temporal conditioning dimension
  ctf_dim: 0      # Temporal-frequency conditioning dimension
  cx_dim: 0       # Cross-attention conditioning dimension
  in_channels: 1  # Input channels
  # transformer parameters
  patch_size: [4, 4]  # [time_patch, freq_patch]
  n_layer: 12         # Number of transformer layers
  n_head: 12          # Number of attention heads
  n_embd: 384         # Embedding dimension
```

## Available Configurations

| Config File | Task | Tokenizer | Conditioning |
|-------------|------|-----------|--------------|
| `bsroformer_text2music.yaml` | Text→Music | Text2Music | Genre labels (y_dim=10) |
| `bsroformer_vocal2music.yaml` | Vocal→Music | Vocal2Music | Vocal mel (ctf_dim=128) |
| `bsroformer_codec2audio.yaml` | Codec→Audio | Codec2Audio | Codec tokens (ct_dim=1024) |
| `bsroformer_superresolution.yaml` | Super-resolution | SuperResolution | Low-quality mel (ctf_dim=128) |
| `bsroformer_midi2audio.yaml` | MIDI→Audio | Midi2Audio | MIDI sequence (cx_dim=256) |
| `bsroformer_mono2stereo.yaml` | Mono→Stereo | Mono2Stereo | Mono mel (ctf_dim=128) |
| `bsroformer_mss.yaml` | Source Separation | MSS | Source labels + mixture (y_dim=4, ctf_dim=128) |

## Usage in Training

In `train.py`, models are instantiated using:

```python
models = instantiate_models(cfg.get("models"))
```

This returns a dictionary with keys `"tokenizer"` and `"bsroformer"`:

```python
tokenizer = models["tokenizer"]
bsroformer = models["bsroformer"]
```

## Conditioning Types

- **y_dim**: One-hot categorical labels (e.g., genre, source type)
- **c_dim**: Global scalar/vector conditioning (e.g., tempo, key)
- **ct_dim**: Temporal conditioning (e.g., codec tokens, text embeddings)
- **ctf_dim**: Temporal-frequency conditioning (e.g., mel spectrograms)
- **cx_dim**: Cross-attention conditioning (e.g., MIDI sequences, text)

## Example Usage

```bash
# Train with text-to-music configuration
python src/train.py models=bsroformer_text2music

# Train with vocal-to-music configuration
python src/train.py models=bsroformer_vocal2music

# Train with custom parameters
python src/train.py models=bsroformer_text2music models.bsroformer.n_layer=24

# Train for source separation
python src/train.py models=bsroformer_mss

# Train for super-resolution with custom embedding dimension
python src/train.py models=bsroformer_superresolution models.bsroformer.n_embd=512
```

## Directory Structure

```
configs/models/
├── README.md                        # Documentation
├── bsroformer_text2music.yaml      # Text-to-music generation
├── bsroformer_vocal2music.yaml     # Vocal-to-music generation
├── bsroformer_codec2audio.yaml     # Codec-to-audio generation
├── bsroformer_superresolution.yaml # Audio super-resolution
├── bsroformer_midi2audio.yaml      # MIDI-to-audio generation
├── bsroformer_mono2stereo.yaml     # Mono-to-stereo conversion
└── bsroformer_mss.yaml             # Music source separation
```

## Benefits

1. **Task-Specific**: Each config is tailored for a specific audio generation task
2. **Self-Contained**: Both tokenizer and model defined in single file
3. **No Redundancy**: No duplicate or unnecessary configurations
4. **Clear Purpose**: Each config has a specific use case and conditioning setup
5. **Easy to Extend**: Simple to add new tasks by creating new config files

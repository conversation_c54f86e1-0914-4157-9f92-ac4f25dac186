# BSRoformer model configuration for MIDI-to-audio generation
# This config defines both the tokenizer and BSRoformer model for MIDI-conditioned audio generation

# Tokenizer for MIDI-to-audio generation
tokenizer:
  _target_: src.models.tokenizers.midi2audio.Midi2AudioTokenizer

# BSRoformer model for MIDI-to-audio generation
bsroformer:
  _target_: src.models.bsroformer_mel.BSRoformerMel
  
  # Condition parameters for MIDI-to-audio (MIDI sequence conditioning)
  y_dim: 0        # One-hot label dimension (disabled)
  c_dim: 0        # Global conditioning dimension (disabled)
  ct_dim: 0       # Temporal conditioning dimension (disabled)
  ctf_dim: 0      # Temporal-frequency conditioning dimension (disabled)
  cx_dim: 256     # Cross-attention conditioning (MIDI sequence features)
  in_channels: 1  # Input channels (mono audio)
  
  # Transformer parameters
  patch_size: [4, 4]  # [time_patch, freq_patch]
  n_layer: 12         # Number of transformer layers
  n_head: 12          # Number of attention heads
  n_embd: 384         # Embedding dimension

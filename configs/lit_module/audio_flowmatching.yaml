_target_: src.lit_module.audio_flowmatching_module.AudioFlowMatchingLitModule

# Optimizer configuration
optimizer:
  _target_: torch.optim.AdamW
  _partial_: true
  lr: 1e-4
  weight_decay: 0.0

# Scheduler configuration (optional)
scheduler:
  _target_: torch.optim.lr_scheduler.LambdaLR
  _partial_: true
  lr_lambda:
    _target_: src.utils.lr_schedulers.LinearWarmupLR
    warmup_steps: 1000

# Model compilation
compile_model: false

# Warmup configuration
warmup:
  enabled: true
  warmup_steps: 1000
  initial_lr: 1e-6

# EMA (Exponential Moving Average) settings
ema_decay: 0.999

# Validation settings
validation_every_n_steps: 2000
save_validation_outputs: true
validation_samples: 4

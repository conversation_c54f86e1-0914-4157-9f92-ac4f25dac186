_target_: src.data.gtzan_datamodule.GTZANDataModule

# data_dir: ${paths.data_dir}  # Use this for dynamic path resolution
data_dir: "/home/<USER>/Workspace/datasets/gtzan" # Path to GTZAN dataset directory

# DataLoader configuration
batch_size: 32 # Needs to be divisible by the number of devices (e.g., if in a distributed setup)
num_workers: 4 # Number of workers for data loading
pin_memory: True # Whether to pin memory for faster GPU transfer
drop_last: False # Whether to drop last incomplete batch

# Audio processing configuration
sr: 22050 # Audio sampling rate
clip_duration: 5.0 # Duration of audio clips in seconds

# Cross-validation configuration
test_fold: 0 # Which fold to use for testing (0-9)
val_fold: 1 # Which fold to use for validation (0-9)

# Infinite sampler configuration (for continuous training)
use_infinite_sampler: False # Whether to use infinite sampler for training
sampler_seed: 42 # Random seed for infinite sampler

# Transform configuration (set to null to use defaults)
# crop: null  # Custom crop function (defaults to AudioCrop with clip_duration)
# train_transform: null  # Transform for training data (defaults to ToMono)
# val_transform: null    # Transform for validation data (defaults to ToMono)
# test_transform: null   # Transform for test data (defaults to ToMono)
# target_transform: null # Transform for targets/labels (defaults to OneHot)

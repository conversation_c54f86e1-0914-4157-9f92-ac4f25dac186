# Loss Function Configurations

This directory contains Hydra configuration files for loss functions used in the Audio Flow Matching Lightning project. The loss system supports both single and multiple loss functions with configurable weights (betas).

## Configuration Structure

Each loss config file follows this structure:

```yaml
defaults:
  - loss_fn/mse_mean  # Include specific loss function configs
  - _self_

beta:
  MSE: 1.0  # Weight for each loss function
```

## Available Loss Functions

### Individual Loss Functions (`loss_fn/`)

| Config File | Loss Function | Description |
|-------------|---------------|-------------|
| `mse_mean.yaml` | MSELoss | MSE loss with mean reduction |
| `mse_sum.yaml` | MSELoss | MSE loss with sum reduction |
| `mse_none.yaml` | MSELoss | MSE loss with no reduction |
| `cross_entropy.yaml` | CrossEntropyLoss | Cross-entropy loss for classification tasks |

### Complete Loss Configurations

| Config File | Loss Functions | Use Case |
|-------------|----------------|----------|
| `default.yaml` | MSE (β=1.0) | Standard flow matching training using MSELoss |

## Flow Matching Loss

The primary loss function for this project is the standard Mean Squared Error (MSE) loss:

```python
loss = torch.mean((vt - ut) ** 2)
```

Where:
- `vt`: Model predictions (velocity field)
- `ut`: Target velocity field

This is implemented using PyTorch's built-in `torch.nn.MSELoss` with `reduction='mean'`.

## Usage in Training

In `train.py`, losses are instantiated using:

```python
losses = instantiate_losses(cfg.get("losses"))
```

This returns a dictionary with structure:

```python
{
    "loss_fn": {
        "MSE": <torch.nn.MSELoss instance>,
        # ... other loss functions
    },
    "beta": {
        "MSE": 1.0,
        # ... corresponding weights
    }
}
```

## Example Usage

```bash
# Use default MSE loss for flow matching
python src/train.py losses=default

# Override loss weight
python src/train.py losses=default losses.beta.MSE=0.5

# Use different MSE reduction by switching loss function
python src/train.py losses=default losses=loss_fn/mse_sum

# Create custom loss config with different reduction
python src/train.py losses=default losses.defaults[0]=loss_fn/mse_none
```

## Lightning Module Integration

In your Lightning module, use the losses like this:

```python
def training_step(self, batch, batch_idx):
    vt = self.model(...)  # Model predictions
    ut = ...              # Target values

    total_loss = 0.0
    for loss_name, loss_fn in self.losses["loss_fn"].items():
        beta = self.losses["beta"][loss_name]
        loss_value = loss_fn(vt, ut) * beta
        total_loss += loss_value

        # Log individual losses
        self.log(f"train/{loss_name}_loss", loss_value)

    self.log("train/total_loss", total_loss)
    return total_loss
```

## Adding New Loss Functions

For custom loss functions, you can either:

1. **Use PyTorch built-in losses** directly in the config:
```yaml
loss_fn:
  YourLoss:
    _target_: torch.nn.SomeLoss
    param1: value1
```

2. **Create custom loss classes** in `src/losses/loss.py` for specialized implementations:
```yaml
loss_fn:
  YourLoss:
    _target_: src.losses.loss.YourLossClass
    param1: value1
```

# Loss Function Configurations

This directory contains Hydra configuration files for loss functions used in the Audio Flow Matching Lightning project. The loss system supports both single and multiple loss functions with configurable weights (betas).

## Configuration Structure

Each loss config file follows this structure:

```yaml
defaults:
  - loss_fn/flow_matching  # Include specific loss function configs
  - _self_

beta:
  FlowMatching: 1.0  # Weight for each loss function
```

## Available Loss Functions

### Individual Loss Functions (`loss_fn/`)

| Config File | Loss Function | Description |
|-------------|---------------|-------------|
| `flow_matching.yaml` | FlowMatchingLoss | Flow matching loss: `E[(vt - ut)^2]` |
| `mse.yaml` | MSELoss | Mean squared error loss (equivalent to flow matching) |
| `cross_entropy.yaml` | CrossEntropyLoss | Cross-entropy loss for classification tasks |

### Complete Loss Configurations

| Config File | Loss Functions | Use Case |
|-------------|----------------|----------|
| `default.yaml` | FlowMatching (β=1.0) | Standard flow matching training |
| `mse.yaml` | MSE (β=1.0) | Alternative MSE-based training |
| `multi_loss.yaml` | FlowMatching (β=1.0) + MSE (β=0.1) | Multi-objective training |

## Flow Matching Loss

The primary loss function for this project is the Flow Matching Loss:

```python
loss = torch.mean((vt - ut) ** 2)
```

Where:
- `vt`: Model predictions (velocity field)
- `ut`: Target velocity field

This loss is implemented in `src.losses.loss.FlowMatchingLoss` with support for different reduction modes:
- `mean`: Average over all elements (default)
- `sum`: Sum all elements
- `none`: No reduction, return per-element losses

## Usage in Training

In `train.py`, losses are instantiated using:

```python
losses = instantiate_losses(cfg.get("losses"))
```

This returns a dictionary with structure:

```python
{
    "loss_fn": {
        "FlowMatching": <FlowMatchingLoss instance>,
        # ... other loss functions
    },
    "beta": {
        "FlowMatching": 1.0,
        # ... corresponding weights
    }
}
```

## Example Usage

```bash
# Use default flow matching loss
python src/train.py losses=default

# Use MSE loss instead
python src/train.py losses=mse

# Use multi-loss configuration
python src/train.py losses=multi_loss

# Override loss weight
python src/train.py losses=default losses.beta.FlowMatching=0.5

# Use different reduction
python src/train.py losses=default losses.loss_fn.FlowMatching.reduction=sum
```

## Lightning Module Integration

In your Lightning module, use the losses like this:

```python
def training_step(self, batch, batch_idx):
    vt = self.model(...)  # Model predictions
    ut = ...              # Target values
    
    total_loss = 0.0
    for loss_name, loss_fn in self.losses["loss_fn"].items():
        beta = self.losses["beta"][loss_name]
        loss_value = loss_fn(vt, ut) * beta
        total_loss += loss_value
        
        # Log individual losses
        self.log(f"train/{loss_name}_loss", loss_value)
    
    self.log("train/total_loss", total_loss)
    return total_loss
```

## Adding New Loss Functions

1. **Create the loss class** in `src/losses/loss.py`
2. **Add to exports** in `src/losses/__init__.py`
3. **Create config file** in `configs/losses/loss_fn/your_loss.yaml`
4. **Create complete config** in `configs/losses/your_config.yaml`

Example new loss function config:

```yaml
# configs/losses/loss_fn/your_loss.yaml
YourLoss:
  _target_: src.losses.loss.YourLossClass
  param1: value1
  param2: value2
```

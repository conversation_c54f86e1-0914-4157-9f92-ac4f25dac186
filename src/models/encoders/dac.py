"""Descript Audio Codec (DAC) encoder/decoder for neural audio compression.

This module provides a wrapper around the DAC model for converting between
audio waveforms and discrete codec tokens. DAC is a neural audio codec that
compresses audio into discrete tokens while maintaining high quality.
"""

from __future__ import annotations

import dac
import torch
from torch import LongTensor, Tensor, nn


class DAC(nn.Module):
    """Descript Audio Codec (DAC) wrapper for neural audio compression.

    This class provides an interface to the DAC model for encoding audio
    to discrete tokens and decoding back to audio. DAC uses vector quantization
    to compress audio into a discrete latent space.

    Currently supports only 44.1kHz audio and mono input.
    """

    def __init__(self, sr: float, n_quantizers: int) -> None:
        """Initialize the DAC codec.

        Args:
            sr: Sample rate in Hz. Must be 44100.
            n_quantizers: Number of quantizer layers to use for encoding.

        Raises:
            AssertionError: If sample rate is not 44100.
        """
        super().__init__()

        assert sr == 44100, f"Only 44.1kHz sample rate is supported, got {sr}"

        model_path = dac.utils.download(model_type="44khz")
        self.codec = dac.DAC.load(str(model_path))  # Convert Path to str
        self.n_quantizers = n_quantizers

    def encode(self, audio: Tensor) -> LongTensor:
        """Encode audio waveform to discrete codec tokens.

        Compresses audio into discrete tokens using vector quantization.
        The number of quantizer layers used is controlled by self.n_quantizers.

        Args:
            audio: Audio waveform tensor of shape (batch_size, channels, audio_length).
                   Must be mono audio (channels=1).

        Returns:
            Discrete codec tokens of shape (batch_size, n_quantizers, time_steps).

        Raises:
            AssertionError: If audio is not mono (channels != 1).
        """
        assert audio.shape[1] == 1, f"Expected mono audio, got {audio.shape[1]} channels"

        with torch.no_grad():
            self.codec.eval()
            _, codes, _, _, _ = self.codec.encode(
                audio_data=audio, n_quantizers=self.n_quantizers
            )  # codes: (b, q, t), integer, codebook indices

        # Slice to requested number of quantizers if needed
        if self.n_quantizers:
            codes = codes[:, :self.n_quantizers, :]  # (b, q, t)

        return codes

    def decode(self, codes: LongTensor) -> Tensor:
        """Decode discrete codec tokens back to audio waveform.

        Reconstructs audio from discrete tokens using the DAC decoder.
        The tokens are first converted to continuous latent representations,
        then decoded to audio.

        Args:
            codes: Discrete codec tokens of shape (batch_size, n_quantizers, time_steps).

        Returns:
            Reconstructed audio waveform of shape (batch_size, channels, audio_length).
        """
        with torch.no_grad():
            self.codec.eval()
            # Convert discrete codes to continuous latent representation
            z, _, _ = self.codec.quantizer.from_codes(codes)  # (b, d, t)
            # Decode latent representation to audio
            audio = self.codec.decode(z)  # (b, c, l)

        return audio

"""BigVGAN-based mel spectrogram encoder/decoder for audio processing.

This module provides a wrapper around the BigVGAN model for converting between
audio waveforms and mel spectrograms. It includes normalization/denormalization
for stable training with flow matching and diffusion models.
"""

import bigvgan
import torch
from bigvgan.meldataset import get_mel_spectrogram
from einops import rearrange
from torch import Tensor, nn

from src.utils.utils import suppress_print


class Mel_BigVGAN_44kHz(nn.Module):
    """BigVGAN-based mel spectrogram encoder/decoder for 44kHz audio.

    This class wraps the BigVGAN model to provide audio-to-mel and mel-to-audio
    conversion capabilities. It includes normalization for stable training with
    generative models.

    The model operates on 44kHz audio and produces mel spectrograms with 128 bands.
    """

    def __init__(self) -> None:
        """Initialize the BigVGAN model with pretrained weights."""
        super().__init__()

        self.model = bigvgan.BigVGAN.from_pretrained(
            "nvidia/bigvgan_v2_44khz_128band_512x", use_cuda_kernel=False
        )
        self.model.remove_weight_norm()

    def encode(self, audio: Tensor) -> Tensor:
        """Encode audio waveform to normalized mel spectrogram.

        Converts multi-channel audio to mel spectrograms using the BigVGAN model's
        mel spectrogram computation. The output is normalized for stable training.

        Args:
            audio: Audio waveform tensor of shape (batch_size, channels, audio_length)

        Returns:
            Normalized mel spectrogram tensor of shape (batch_size, channels, time_frames, mel_bins)
        """
        with torch.no_grad(), suppress_print():
            self.model.eval()
            # Process each channel separately
            mel_spectrograms = [
                get_mel_spectrogram(audio[:, i, :], self.model.h) for i in range(audio.shape[1])
            ]
            x = torch.stack(mel_spectrograms, dim=1)  # (b, c, f, t)

        # Rearrange to (batch, channels, time, frequency)
        x = rearrange(x, "b c f t -> b c t f")
        x = self.normalize(x)

        return x

    def decode(self, x: Tensor) -> Tensor:
        """Decode normalized mel spectrogram to audio waveform.

        Converts mel spectrograms back to audio using the BigVGAN vocoder.
        The input is first denormalized before vocoding.

        Args:
            x: Normalized mel spectrogram tensor of shape
                (batch_size, channels, time_frames, mel_bins)

        Returns:
            Audio waveform tensor of shape (batch_size, channels, audio_length)
        """
        x = self.denormalize(x)
        # Rearrange to (batch, channels, frequency, time) for BigVGAN
        x = rearrange(x, "b c t f -> b c f t")

        with torch.no_grad():
            self.model.eval()
            # Process each channel separately and concatenate
            audio_channels = [self.model(x[:, i, :, :]) for i in range(x.shape[1])]
            audio = torch.cat(audio_channels, dim=1)  # (b, c, l)

        return audio

    def normalize(self, x: Tensor) -> Tensor:
        """Normalize log mel spectrogram to range approximately [-1, 1].

        This normalization stabilizes training for flow matching and diffusion models
        by centering the mel spectrogram values around zero.

        Args:
            x: Raw mel spectrogram tensor

        Returns:
            Normalized mel spectrogram tensor
        """
        return (x + 5) / 5

    def denormalize(self, x: Tensor) -> Tensor:
        """Denormalize mel spectrogram back to original scale.

        Reverses the normalization applied by the normalize method.

        Args:
            x: Normalized mel spectrogram tensor

        Returns:
            Denormalized mel spectrogram tensor
        """
        return x * 5 - 5

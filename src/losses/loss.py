"""Loss functions for flow matching and diffusion models.

This module provides loss functions specifically designed for flow matching
and diffusion-based generative models.
"""

import torch
from torch import Tensor, nn


class FlowMatchingLoss(nn.Module):
    """Flow Matching Loss for continuous normalizing flows.

    Computes the mean squared error between model predictions (vt) and targets (ut).
    This is the standard loss function used in flow matching and rectified flow models.

    Loss = E[(vt - ut)^2]

    where:
    - vt: model prediction (velocity field)
    - ut: target velocity field
    """

    def __init__(self, reduction: str = "mean") -> None:
        """Initialize the Flow Matching Loss.

        Args:
            reduction: Specifies the reduction to apply to the output.
                      'mean': the sum of the output will be divided by the number of elements
                      'sum': the output will be summed
                      'none': no reduction will be applied
        """
        super().__init__()
        self.reduction = reduction

    def forward(self, vt: Tensor, ut: Tensor) -> Tensor:
        """Compute the flow matching loss.

        Args:
            vt: Model predictions (velocity field) of shape (..., *)
            ut: Target velocity field of shape (..., *)

        Returns:
            Loss tensor. Shape depends on reduction:
            - 'mean' or 'sum': scalar tensor
            - 'none': same shape as input tensors
        """
        # Compute squared error
        loss = (vt - ut) ** 2

        # Apply reduction
        if self.reduction == "mean":
            return torch.mean(loss)
        if self.reduction == "sum":
            return torch.sum(loss)
        if self.reduction == "none":
            return loss
        raise ValueError(f"Invalid reduction mode: {self.reduction}")


class MSELoss(nn.Module):
    """Mean Squared Error Loss.

    Simple wrapper around torch.nn.MSELoss for consistency with the project structure.
    Equivalent to FlowMatchingLoss with reduction='mean'.
    """

    def __init__(self, reduction: str = "mean") -> None:
        """Initialize the MSE Loss.

        Args:
            reduction: Specifies the reduction to apply to the output.
        """
        super().__init__()
        self.mse_loss = nn.MSELoss(reduction=reduction)

    def forward(self, prediction: Tensor, target: Tensor) -> Tensor:
        """Compute the MSE loss.

        Args:
            prediction: Model predictions of shape (..., *)
            target: Target values of shape (..., *)

        Returns:
            Loss tensor.
        """
        return self.mse_loss(prediction, target)

"""Customized Progress Bar."""

import sys
from typing import Any, Dict, Optional

import lightning.pytorch as pl
from lightning.pytorch.callbacks import RichProgressBar, TQDMProgressBar
from lightning.pytorch.callbacks.progress.rich_progress import (
    BatchesProcessedColumn,
    CustomBarColumn,
    CustomTimeColumn,
    ProcessingSpeedColumn,
    RichProgressBarTheme,
)
from lightning.pytorch.callbacks.progress.tqdm_progress import Tqdm
from rich.console import RenderableType
from rich.progress import Task, TextColumn
from rich.style import Style
from rich.text import Text

rich_theme = RichProgressBarTheme(
    description="green_yellow",
    progress_bar="green1",
    progress_bar_finished="green1",
    progress_bar_pulse="#6206E0",
    batch_progress="green_yellow",
    time="#f5ad05",
    processing_speed="#0cc6f5",
    metrics="#fa69fa",
)


class CustomBatchesProcessedColumn(BatchesProcessedColumn):
    """Customized BatchesProcessedColumn with percentage added.

    Args:
        BatchesProcessedColumn (_type_): _description_
    """

    def __init__(self, style: str | Style):
        super().__init__(style)

    def render(self, task: "Task") -> RenderableType:
        # return super().render(task)
        total = task.total if task.total != float("inf") else "--"
        return Text(f"{task.percentage:>3.0f}% ({int(task.completed)}/{total})", style=self.style)


class CustomRichProgressBar(RichProgressBar):
    """Customized rich progress bar with dynamic precision for metrics."""

    def __init__(
        self,
        precision: int = 6,  # New parameter for controlling precision dynamically
        refresh_rate: int = 1,
        leave: bool = False,
        theme: RichProgressBarTheme = rich_theme,
        console_kwargs: dict[str, Any] | None = None,
    ) -> None:
        super().__init__(refresh_rate, leave, theme, console_kwargs)
        self.precision = precision  # Store the precision value

    def configure_columns(self, trainer: "pl.Trainer") -> list:
        """Configure the columns in the progress bar, including custom formatting."""
        return [
            TextColumn("[progress.description]{task.description}"),
            CustomBarColumn(
                complete_style=self.theme.progress_bar,
                finished_style=self.theme.progress_bar_finished,
                pulse_style=self.theme.progress_bar_pulse,
            ),
            CustomBatchesProcessedColumn(style=self.theme.batch_progress),
            CustomTimeColumn(style=self.theme.time),
            ProcessingSpeedColumn(style=self.theme.processing_speed),
        ]

    def get_metrics(self, trainer: pl.Trainer, pl_module: pl.LightningModule = None):
        """Override to format all metrics with dynamic precision."""
        metrics = super().get_metrics(trainer, pl_module)

        # Format all metrics with the specified precision
        for key, value in metrics.items():
            if isinstance(value, (int, float)):  # Only format numerical values
                # Apply dynamic precision for all metrics except 'v_num'
                if key == "v_num":
                    # Format v_num to always show two decimal places
                    metrics[key] = f"{value:.0f}"
                else:
                    metrics[key] = f"{value:.{self.precision}f}"

        return metrics


class CustomTQDMProgressBar(TQDMProgressBar):
    """Customized lightning progress bar. Sometimes, dynamic_ncols in tqdm does not work. It is
    better to use rich progress bar.

    Args:
        TQDMProgressBar (_type_): _description_
    """

    def init_train_tqdm(self) -> Tqdm:
        """Override this to customize the tqdm bar for training."""
        return Tqdm(
            desc=self.train_description,
            position=(2 * self.process_position),
            disable=self.is_disabled,
            leave=True,
            dynamic_ncols=True,
            file=sys.stdout,
            smoothing=0,
            colour="yellow",
        )

    def init_validation_tqdm(self) -> Tqdm:
        # The train progress bar doesn't exist in `trainer.validate()`
        has_main_bar = self.trainer.state.fn != "validate"
        return Tqdm(
            desc=self.validation_description,
            position=(2 * self.process_position + has_main_bar),
            disable=self.is_disabled,
            leave=not has_main_bar,
            dynamic_ncols=True,
            file=sys.stdout,
            colour="green",
        )

from collections.abc import Callable
from datetime import timedelta
from timeit import default_timer as timer
from typing import Any, Dict, Optional, Tuple, Union

import numpy as np
import torch
from lightning import LightningModule
from lightning.pytorch.core.optimizer import LightningOptimizer
from omegaconf import DictConfig
from pytorch_lightning.utilities import rank_zero_only
from torch import nn
from torch.optim.optimizer import Optimizer
from torchmetrics import MetricCollection
from torchmetrics.aggregation import MaxMetric, MeanMetric

from src.utils import RankedLogger

log = RankedLogger(__name__, rank_zero_only=True)


class MNISTLitModule(LightningModule):
    """Example of a `LightningModule` for MNIST classification.

    A `LightningModule` implements 8 key methods:

    ```python
    def __init__(self):
    # Define initialization code here.

    def setup(self, stage):
    # Things to setup before each stage, 'fit', 'validate', 'test', 'predict'.
    # This hook is called on every process when using DDP.

    def training_step(self, batch, batch_idx):
    # The complete training step.

    def validation_step(self, batch, batch_idx):
    # The complete validation step.

    def test_step(self, batch, batch_idx):
    # The complete test step.

    def predict_step(self, batch, batch_idx):
    # The complete predict step.

    def configure_optimizers(self):
    # Define and configure optimizers and LR schedulers.
    ```

    Docs:
        https://lightning.ai/docs/pytorch/latest/common/lightning_module.html
    """

    default_monitor: str = "val/MultiClassAccuracy"

    def __init__(
        self,
        models: dict[str, nn.Module],
        losses: dict[str, dict[str, nn.Module]],
        metrics: dict[str, nn.Module],
        optimizer: torch.optim.Optimizer,
        scheduler: torch.optim.lr_scheduler,
        compile_model: bool,
        warmup: DictConfig[str, Any],
    ) -> None:
        """Initialize a `MNISTLitModule`.

        :param models: The models to train.
        :param losses: The loss functions to use for training.
        :param metrics: The metrics to use for evaluating.
        :param optimizer: The optimizer to use for training.
        :param scheduler: The learning rate scheduler to use for training.
        """
        super().__init__()

        # this line allows to access init params with 'self.hparams' attribute
        # also ensures init params will be stored in ckpt
        self.save_hyperparameters(logger=False)

        # model
        self.model = list(models.values())[0]

        # optimizer
        self.optimizer = optimizer(params=self.model.parameters())

        # scheduler
        self.scheduler = scheduler

        # compile model
        self.compile_model = compile_model

        # warmup
        self.warmup = warmup
        self.init_lr = self.optimizer.param_groups[0]["lr"]

        # loss functions
        self.loss_fn_dict = losses["loss_fn"]
        self.loss_beta_dict = losses["beta"]
        self._init_losses()

        # metric functions
        self.metrics = metrics
        self._init_metrics()

        # for tracking best so far validation accuracy
        self.val_acc_best = MaxMetric()

        # for training and validation time tracking
        self.train_epoch_start_time = 0
        self.train_epoch_end_time = 0
        self.valid_epoch_start_time = 0
        self.valid_epoch_end_time = 0

    def _init_losses(self):
        """Initialize the loss dict."""
        # train loss dict, for averaging loss across batches
        self.train_loss_collect = MetricCollection(
            {
                "loss_all": MeanMetric(),
            },
            prefix="train/",
        )

        for loss_name in self.loss_fn_dict.keys():
            self.train_loss_collect["loss_" + loss_name] = MeanMetric()

        # val loss dict, for averaging loss across batches
        self.val_loss_collect = self.train_loss_collect.clone(prefix="val/")

        # test loss dict, for averaging loss across batches
        self.test_loss_collect = self.train_loss_collect.clone(prefix="test/")

    def _init_metrics(self):
        """Initialize the metric dict."""
        # train metric dict, for averaging metrics across batches
        self.train_metric_collect = MetricCollection(
            {},
            prefix="train/",
        )
        for metric_name, metric_fn in self.metrics.items():
            self.train_metric_collect[metric_name] = metric_fn()

        # val metric dict, for averaging metrics across batches
        self.val_metric_collect = self.train_metric_collect.clone(prefix="val/")

        # test metric dict, for averaging metrics across batches
        self.test_metric_collect = self.train_metric_collect.clone(prefix="test/")

    def setup(self, stage: str) -> None:
        """Lightning hook that is called at the beginning of fit (train + validate), validate,
        test, or predict.

        This is a good hook when you need to build models dynamically or adjust something about
        them. This hook is called on every process when using DDP.

        :param stage: Either `"fit"`, `"validate"`, `"test"`, or `"predict"`.
        """
        if self.compile_model and stage == "fit":
            self.model = torch.compile(self.model)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Perform a forward pass through the model `self.model`.

        :param x: A tensor of images.
        :return: A tensor of logits.
        """
        return self.model(x)

    def _compute_loss(
        self, preds: torch.Tensor, targets: torch.Tensor, mode: str = "train"
    ) -> dict[str, torch.Tensor]:
        """Compute loss.

        Args:
            preds (torch.Tensor): predictions
            targets (torch.Tensor): targets
            mode (str, optional): 'train' | 'val'. Defaults to 'train'.
        """
        loss_dict = {mode + "/loss_all": 0.0}
        for (loss_name, loss_fn), (_, loss_beta) in zip(
            self.loss_fn_dict.items(), self.loss_beta_dict.items()
        ):
            loss_dict[mode + "/loss_" + loss_name] = loss_fn(preds, targets)
            loss_dict[mode + "/loss_all"] += loss_beta * loss_dict[mode + "/loss_" + loss_name]
        return loss_dict

    def model_step(
        self, batch: tuple[torch.Tensor, torch.Tensor], mode: str = "train"
    ) -> tuple[dict[str, torch.Tensor], torch.Tensor, torch.Tensor]:
        """Perform a single model step on a batch of data.

        :param batch: A batch of data (a tuple) containing the input tensor of images
                      and target labels.

        :return: A tuple containing (in order):
            - A dictionary of losses, Dict[str, torch.Tensor].
            - A tensor of predictions, torch.Tensor
            - A tensor of target labels, torch.Tensor
        """
        inputs, targets = batch
        logits = self.forward(inputs)
        loss_dict = self._compute_loss(logits, targets, mode)
        preds = torch.argmax(logits, dim=1)
        return loss_dict, preds, targets

    def configure_optimizers(self) -> dict[str, Any]:
        """Choose what optimizers and learning-rate schedulers to use in your optimization.
        Normally you'd need one. But in the case of GANs or similar you might have multiple.

        Examples:
            https://lightning.ai/docs/pytorch/latest/common/lightning_module.html#configure-optimizers

        :return: A dict containing the configured optimizers and learning-rate schedulers to be
                 used for training.
        """
        if self.scheduler is not None:
            scheduler = self.scheduler(optimizer=self.optimizer)
            return {
                "optimizer": self.optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "interval": "epoch",
                    "frequency": 1,
                    "monitor": self.default_monitor,
                    "strict": True,
                    "name": None,
                },
            }
        return {"optimizer": self.optimizer}

    def on_train_start(self) -> None:
        """Lightning hook that is called when training begins."""
        # by default lightning executes validation step sanity checks before training starts,
        # so it's worth to make sure validation metrics don't store results from these checks
        for val_loss in self.val_loss_collect.values():
            val_loss.reset()
        for val_metric in self.val_metric_collect.values():
            val_metric.reset()
        self.val_acc_best.reset()

    def on_train_epoch_start(self) -> None:
        "Lightning hook that is called when a training epoch starts."
        # for training time tracking
        self.train_epoch_start_time = timer()

    def training_step(
        self, batch: tuple[torch.Tensor, torch.Tensor], batch_idx: int
    ) -> torch.Tensor:
        """Perform a single training step on a batch of data from the training set.

        :param batch: A batch of data (a tuple) containing the input tensor of images and target
            labels.
        :param batch_idx: The index of the current batch.
        :return: A tensor of losses between model predictions and targets.
        """
        loss_dict, preds, targets = self.model_step(batch, mode="train")
        loss = loss_dict["train/loss_all"]

        # update and log loss in step
        for loss_name, loss_value in loss_dict.items():
            # torchmetrics does not reset after each step,
            # so we just log a scalar loss value on each step
            self.log(
                loss_name + "_step",
                loss_value,
                on_step=True,
                on_epoch=False,
                prog_bar=False,
                sync_dist=True,
            )
            # if you use torchmetrics, you can log metrics with `self.log` method and don't need to
            # worry about sync_dist parameter. torchmetrics will automatically synchronize metrics
            # over multiple GPUs. However, if you use custom metrics, you need to set sync_dist=True
            self.train_loss_collect[loss_name](loss_value)
            self.log(
                loss_name + "_epoch",
                self.train_loss_collect[loss_name],
                on_step=False,
                on_epoch=True,
                prog_bar=False,
            )

        # update and log metrics
        self.train_metric_collect(preds, targets)
        self.log_dict(self.train_metric_collect, on_step=False, on_epoch=True, prog_bar=False)

        # update and log lr
        lr_step = self.optimizer.param_groups[0]["lr"]
        self.log("train/lr_step", lr_step, on_step=True, on_epoch=False, prog_bar=True)

        # return loss or backpropagation will fail
        return loss

    def optimizer_step(
        self,
        epoch: int,
        batch_idx: int,
        optimizer: Optimizer | LightningOptimizer,
        optimizer_closure: Callable[[], Any] | None = None,
    ) -> None:
        """Override the optimizer step to include warmup."""
        # manually warm up lr without a scheduler
        warmup_steps = self.warmup.get("steps", 0)
        if self.warmup.get("enable", False) and self.trainer.global_step < warmup_steps:
            lr_scale = min(1.0, float(self.trainer.global_step + 1) / warmup_steps)
            for pg in optimizer.param_groups:
                pg["lr"] = lr_scale * self.init_lr

        # update params
        optimizer.step(closure=optimizer_closure)

    def on_validation_epoch_start(self) -> None:
        "Lightning hook that is called when a validation epoch starts."
        # for training time tracking
        self.train_epoch_end_time = timer()

        # for validation time tracking
        self.valid_epoch_start_time = timer()

    def validation_step(self, batch: tuple[torch.Tensor, torch.Tensor], batch_idx: int) -> None:
        """Perform a single validation step on a batch of data from the validation set.

        :param batch: A batch of data (a tuple) containing the input tensor of images and target
            labels.
        :param batch_idx: The index of the current batch.
        """
        loss_dict, preds, targets = self.model_step(batch, mode="val")

        # update and log loss
        for loss_name, loss_value in loss_dict.items():
            self.val_loss_collect[loss_name](loss_value)
        self.log_dict(self.val_loss_collect, on_step=False, on_epoch=True, prog_bar=False)

        # update and log metrics
        self.val_metric_collect(preds, targets)
        self.log_dict(self.val_metric_collect, on_step=False, on_epoch=True, prog_bar=False)

    def on_validation_epoch_end(self) -> None:
        """Lightning hook that is called when a validation epoch ends.

        Note that this method is called before on_train_epoch_end().
        """
        # for validation time tracking
        self.valid_epoch_end_time = timer()

        # log best so far validation accuracy
        acc = self.val_metric_collect["MultiClassAccuracy"].compute()  # get current val acc
        self.val_acc_best.update(acc)  # update best so far val acc
        best_acc = self.val_acc_best.compute()
        # log `val_acc_best` as a value through `.compute()` method, instead of as a metric object
        # otherwise metric would be reset by lightning after each epoch
        # To-Do: test sync_dist=True or False
        self.log("val/acc_best", best_acc, on_step=False, on_epoch=True, prog_bar=False)

        # log hp_metric to loggers for hparams selection.
        hp_metric = self.trainer.callback_metrics.get(self.default_monitor, None)
        if hp_metric is not None:
            self.trainer.logger.log_metrics(
                {"hp_metric": hp_metric}, step=self.trainer.global_step
            )

        # Print&log metrics
        self._print_metrics()

        # If you use self.log with torchmetrics object, such as
        # ```loss = MeanMetric()
        #    loss(value)
        #    self.log('loss', loss)
        # ```
        # loss are automatically reset by torchmetrics after each epoch
        # Otherwise you need to reset them manually by calling `reset()` method
        # I always reset them
        self.val_loss_collect.reset()
        self.val_metric_collect.reset()

    def on_train_epoch_end(self) -> None:
        "Lightning hook that is called when a training epoch ends."
        # If you use self.log with torchmetrics object, such as
        # ```loss = MeanMetric()
        #    loss(value)
        #    self.log('loss', loss)
        # ```
        # loss are automatically reset by torchmetrics after each epoch
        # Otherwise you need to reset them manually by calling `reset()` method
        # I always reset them
        self.train_loss_collect.reset()
        self.train_metric_collect.reset()

    def test_step(self, batch: tuple[torch.Tensor, torch.Tensor], batch_idx: int) -> None:
        """Perform a single test step on a batch of data from the test set.

        :param batch: A batch of data (a tuple) containing the input tensor of images and target
            labels.
        :param batch_idx: The index of the current batch.
        """
        loss_dict, preds, targets = self.model_step(batch, mode="test")

        # update and log loss
        for loss_name, loss_value in loss_dict.items():
            self.test_loss_collect[loss_name](loss_value)
            self.log(
                loss_name,
                self.test_loss_collect[loss_name],
                on_step=False,
                on_epoch=True,
                prog_bar=False,
            )

        # update and log metrics
        self.test_metric_collect(preds, targets)
        self.log_dict(self.test_metric_collect, on_step=False, on_epoch=True, prog_bar=False)

    def on_test_epoch_end(self) -> None:
        """Lightning hook that is called when a test epoch ends."""
        # If you use self.log with torchmetrics object, such as
        # ```loss = MeanMetric()
        #    loss(value)
        #    self.log('loss', loss)
        # ```
        # loss are automatically reset by torchmetrics after each epoch
        # Otherwise you need to reset them manually by calling `reset()` method
        # I always reset them
        self.test_loss_collect.reset()
        self.test_metric_collect.reset()

    @rank_zero_only
    def _print_metrics(self):
        """Print losses and metrics to stdout and a log file."""

        global_step = self.trainer.global_step
        current_epoch = self.trainer.current_epoch
        steps_per_epoch = (
            int(
                np.rint(
                    self.trainer.limit_train_batches
                    * len(self.trainer.datamodule.train_dataloader())
                )
                // self.trainer.world_size
            )
            if isinstance(self.trainer.limit_train_batches, float)
            else self.trainer.limit_train_batches
        )
        local_step = global_step % steps_per_epoch

        # Train loss, metrics, and time
        train_str = "Train: "
        train_time = str(timedelta(seconds=0))
        if self.trainer.sanity_checking:
            train_str += "Sanity check, no training data."
        else:
            for key, fn in {**self.train_loss_collect, **self.train_metric_collect}.items():
                v = fn.compute()
                self.trainer.logger.log_metrics({key: v}, step=self.trainer.global_step)
                train_str += f"{key.split('/')[1]}:  {v:.5f},  "
            train_time = str(
                timedelta(seconds=round(self.train_epoch_end_time - self.train_epoch_start_time))
            )

        # Validation loss, metrics, and time
        valid_str = "Valid: "
        for key, fn in {**self.val_loss_collect, **self.val_metric_collect}.items():
            v = fn.compute()
            self.trainer.logger.log_metrics({key: v}, step=self.trainer.global_step)
            valid_str += f"{key.split('/')[1]}:  {v:.5f},  "
        valid_time = str(
            timedelta(seconds=round(self.valid_epoch_end_time - self.valid_epoch_start_time))
        )

        lr = self.optimizer.param_groups[0]["lr"]

        log.info("\n")
        log.info("-" * 120)
        log.info(
            f"Global Step: {global_step},  "
            f"Current Epoch: {current_epoch},  "
            f"Local Step / Total Steps In An Epoch: "
            f"{local_step}/{steps_per_epoch}"
        )
        log.info(train_str)
        log.info(valid_str)
        log.info(f"Train time: {train_time},  " f"Valid time: {valid_time},  " f"Lr: {lr}")
        log.info("-" * 120)
        log.info("\n")


if __name__ == "__main__":
    _ = MNISTLitModule(None, None, None, None, None, None, None)

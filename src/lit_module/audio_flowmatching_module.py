from collections.abc import Callable
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from timeit import default_timer as timer
from typing import Any, Dict, Optional, Tuple, Union

import matplotlib.pyplot as plt
import numpy as np
import soundfile
import torch
import torchdiffeq
from lightning import LightningModule
from lightning.pytorch.core.optimizer import LightningOptimizer
from omegaconf import DictConfig
from pytorch_lightning.utilities import rank_zero_only
from torch import nn
from torch.optim.optimizer import Optimizer
from torchcfm.conditional_flow_matching import ConditionalFlowMatcher
from torchmetrics import MetricCollection
from torchmetrics.aggregation import MaxMetric, MeanMetric

from src.utils import EMA, RankedLogger

log = RankedLogger(__name__, rank_zero_only=True)


class AudioFlowMatchingLitModule(LightningModule):
    """Lightning Module for Audio Flow Matching.

    This module implements flow matching for audio generation tasks, specifically
    designed for text-to-music generation using mel spectrograms.

    Based on the audio_flow/train.py implementation, this module:
    - Uses ConditionalFlowMatcher for flow matching training
    - Supports EMA (Exponential Moving Average) for model weights
    - Implements validation with ODE integration
    - Focuses on text2music task with GTZAN dataset
    - Does not use metrics for evaluation (following audio_flow pattern)
    """

    def __init__(
        self,
        models: dict[str, nn.Module],
        losses: dict[str, dict[str, nn.Module]],
        metrics: dict[str, nn.Module],  # Will be empty for audio flow matching
        optimizer: torch.optim.Optimizer,
        scheduler: torch.optim.lr_scheduler,
        compile_model: bool,
        warmup: DictConfig[str, Any],
        ema_decay: float = 0.999,
        validation_every_n_steps: int = 2000,
        save_validation_outputs: bool = True,
        validation_samples: int = 4,
    ) -> None:
        """Initialize AudioFlowMatchingLitModule.

        :param models: Dictionary containing the main model and data transform
        :param losses: The loss functions to use for training
        :param metrics: Empty dict (no metrics used in audio flow matching)
        :param optimizer: The optimizer to use for training
        :param scheduler: The learning rate scheduler to use for training
        :param compile_model: Whether to compile the model
        :param warmup: Warmup configuration
        :param ema_decay: Decay rate for EMA model
        :param validation_every_n_steps: How often to run validation
        :param save_validation_outputs: Whether to save validation audio/plots
        :param validation_samples: Number of samples to validate
        """
        super().__init__()

        # Save hyperparameters
        self.save_hyperparameters(logger=False)

        # Extract models
        self.model = models["bsroformer"]  # Main BSRoformer model
        self.data_transform = models["tokenizer"]  # Data transform (Text2Music_Mel)

        # Flow matching
        self.fm = ConditionalFlowMatcher(sigma=0.0)

        # EMA model
        self.ema = EMA(self.model, decay=ema_decay)

        # Optimizer and scheduler
        self.optimizer_class = optimizer
        self.scheduler_class = scheduler

        # Compile model
        self.compile_model = compile_model

        # Warmup
        self.warmup = warmup

        # Loss functions
        self.loss_fn_dict = losses["loss_fn"]
        self.loss_beta_dict = losses["beta"]
        self._init_losses()

        # Validation settings
        self.validation_every_n_steps = validation_every_n_steps
        self.save_validation_outputs = save_validation_outputs
        self.validation_samples = validation_samples

        # EMA decay
        self.ema_decay = ema_decay

        # Training step counter
        self.training_step_count = 0

    def _init_losses(self):
        """Initialize the loss tracking."""
        # Train loss tracking
        self.train_loss_collect = MetricCollection(
            {"loss_all": MeanMetric()},
            prefix="train/",
        )

        for loss_name in self.loss_fn_dict.keys():
            self.train_loss_collect["loss_" + loss_name] = MeanMetric()

        # Val loss tracking
        self.val_loss_collect = self.train_loss_collect.clone(prefix="val/")

    def setup(self, stage: str) -> None:
        """Lightning hook called at the beginning of fit/validate/test/predict."""
        if self.compile_model and stage == "fit":
            self.model = torch.compile(self.model)

    def configure_optimizers(self):
        """Configure optimizers and learning rate schedulers."""
        optimizer = self.optimizer_class(params=self.model.parameters())
        
        if self.scheduler_class is not None:
            scheduler = self.scheduler_class(optimizer=optimizer)
            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "interval": "step",
                    "frequency": 1,
                },
            }
        return optimizer

    def training_step(self, batch: Any, batch_idx: int) -> torch.Tensor:
        """Perform a single training step."""
        # ------ 1. Data preparation ------
        # Transform data into latent representations and conditions
        target, cond_dict, _ = self.data_transform(batch)

        # Noise
        noise = torch.randn_like(target)

        # Get input and velocity using flow matching
        t, xt, ut = self.fm.sample_location_and_conditional_flow(x0=noise, x1=target)

        # ------ 2. Training ------
        # Forward pass
        self.model.train()
        vt = self.model(t=t, x=xt, cond_dict=cond_dict)

        # Compute loss
        loss_dict = {}
        total_loss = 0.0

        for loss_name, loss_fn in self.loss_fn_dict.items():
            beta = self.loss_beta_dict[loss_name]
            loss_value = loss_fn(vt, ut) * beta
            loss_dict[f"loss_{loss_name}"] = loss_value
            total_loss += loss_value

        loss_dict["loss_all"] = total_loss

        # Update EMA
        self.ema.update(self.model)

        # Log losses
        for loss_name, loss_value in loss_dict.items():
            self.train_loss_collect[loss_name](loss_value)
            self.log(
                f"train/{loss_name}_step",
                loss_value,
                on_step=True,
                on_epoch=False,
                prog_bar=True if loss_name == "loss_all" else False,
                sync_dist=True,
            )

        # Log learning rate
        lr = self.optimizers().param_groups[0]["lr"]
        self.log("train/lr", lr, on_step=True, on_epoch=False, prog_bar=True)

        # Increment step counter
        self.training_step_count += 1

        return total_loss

    def on_train_epoch_end(self) -> None:
        """Called when the train epoch ends."""
        # Log epoch losses
        self.log_dict(self.train_loss_collect, on_step=False, on_epoch=True)
        self.train_loss_collect.reset()

    def validation_step(self, batch: Any, batch_idx: int) -> None:
        """Perform a single validation step."""
        if batch_idx >= self.validation_samples:
            return

        # Only validate if it's time
        if self.training_step_count % self.validation_every_n_steps != 0:
            return

        self._validate_sample(batch, batch_idx, "val")

    def _validate_sample(self, batch: Any, batch_idx: int, split: str) -> None:
        """Validate a single sample with ODE integration."""
        device = self.device

        # ------ 1. Data preparation ------
        target, cond_dict, cond_sources = self.data_transform(batch)
        noise = torch.randn_like(target)

        # ------ 2. Forward with ODE (both main model and EMA) ------
        for model_name, model in [("main", self.model), ("ema", self.ema.get_model())]:
            with torch.no_grad():
                model.eval()
                
                # ODE integration
                traj = torchdiffeq.odeint(
                    lambda t, x: model.forward(t, x, cond_dict),
                    y0=noise,
                    t=torch.linspace(0, 1, 2, device=device),
                    atol=1e-4,
                    rtol=1e-4,
                    method="dopri5",
                )

                est_target = traj[-1]  # (b, c, t, f)

                # Convert to audio
                est_audio = self.data_transform.latent_to_audio(est_target).cpu().numpy()
                gt_audio = self.data_transform.latent_to_audio(target).cpu().numpy()

                # Save outputs if enabled
                if self.save_validation_outputs:
                    self._save_validation_outputs(
                        est_audio, gt_audio, cond_sources, 
                        batch_idx, split, model_name
                    )

    def _save_validation_outputs(
        self, 
        est_audio: np.ndarray, 
        gt_audio: np.ndarray, 
        cond_sources: dict,
        batch_idx: int, 
        split: str, 
        model_name: str
    ) -> None:
        """Save validation audio and plots."""
        # Create output directory
        step = self.training_step_count
        out_dir = Path("validation_outputs") / f"step_{step}" / model_name
        out_dir.mkdir(parents=True, exist_ok=True)

        # Get caption if available
        caption = cond_sources.get("caption", [""])[0] if "caption" in cond_sources else ""
        
        # Save audio files
        sr = 44100  # Assuming 44.1kHz sample rate
        
        # Estimated audio
        est_path = out_dir / f"{split}_{batch_idx}_est_{caption}.wav"
        soundfile.write(est_path, est_audio[0].T, sr)
        
        # Ground truth audio
        gt_path = out_dir / f"{split}_{batch_idx}_gt_{caption}.wav"
        soundfile.write(gt_path, gt_audio[0].T, sr)

        log.info(f"Saved validation outputs to {out_dir}")

    def test_step(self, batch: Any, batch_idx: int) -> None:
        """Perform a single test step."""
        self._validate_sample(batch, batch_idx, "test")

    def predict_step(self, batch: Any, batch_idx: int, dataloader_idx: int = 0) -> Any:
        """Perform a single prediction step."""
        device = self.device

        # Data preparation
        target, cond_dict, cond_sources = self.data_transform(batch)
        noise = torch.randn_like(target)

        # Forward with ODE using EMA model
        with torch.no_grad():
            ema_model = self.ema.get_model()
            ema_model.eval()

            traj = torchdiffeq.odeint(
                lambda t, x: ema_model.forward(t, x, cond_dict),
                y0=noise,
                t=torch.linspace(0, 1, 2, device=device),
                atol=1e-4,
                rtol=1e-4,
                method="dopri5",
            )

            est_target = traj[-1]
            est_audio = self.data_transform.latent_to_audio(est_target)

        return {
            "estimated_audio": est_audio,
            "target_audio": self.data_transform.latent_to_audio(target),
            "conditions": cond_sources,
        }

    def on_train_start(self) -> None:
        """Lightning hook called when training begins."""
        # Reset validation loss tracking
        self.val_loss_collect.reset()

    def on_validation_epoch_end(self) -> None:
        """Lightning hook called when validation epoch ends."""
        # Log validation losses if any were computed
        if len(self.val_loss_collect) > 0:
            self.log_dict(self.val_loss_collect, on_step=False, on_epoch=True)
            self.val_loss_collect.reset()

    def on_test_epoch_end(self) -> None:
        """Lightning hook called when test epoch ends."""
        pass

    @rank_zero_only
    def _print_training_info(self) -> None:
        """Print training information."""
        step = self.training_step_count
        epoch = self.current_epoch

        # Get current losses
        train_loss = self.train_loss_collect["loss_all"].compute()
        lr = self.optimizers().param_groups[0]["lr"]

        log.info(f"Step: {step}, Epoch: {epoch}, Loss: {train_loss:.6f}, LR: {lr:.2e}")

    def get_model_for_inference(self, use_ema: bool = True) -> nn.Module:
        """Get model for inference.

        :param use_ema: Whether to use EMA model (recommended)
        :return: Model for inference
        """
        if use_ema:
            return self.ema.get_model()
        else:
            return self.model

    def generate_audio(
        self,
        cond_dict: dict,
        target_shape: tuple,
        use_ema: bool = True,
        num_steps: int = 2,
        method: str = "dopri5"
    ) -> torch.Tensor:
        """Generate audio from conditions.

        :param cond_dict: Conditioning dictionary
        :param target_shape: Shape of target tensor (b, c, t, f)
        :param use_ema: Whether to use EMA model
        :param num_steps: Number of ODE integration steps
        :param method: ODE solver method
        :return: Generated audio tensor
        """
        device = self.device
        model = self.get_model_for_inference(use_ema)

        # Create noise with target shape
        noise = torch.randn(target_shape, device=device)

        with torch.no_grad():
            model.eval()

            # ODE integration
            traj = torchdiffeq.odeint(
                lambda t, x: model.forward(t, x, cond_dict),
                y0=noise,
                t=torch.linspace(0, 1, num_steps, device=device),
                atol=1e-4,
                rtol=1e-4,
                method=method,
            )

            est_target = traj[-1]
            est_audio = self.data_transform.latent_to_audio(est_target)

        return est_audio

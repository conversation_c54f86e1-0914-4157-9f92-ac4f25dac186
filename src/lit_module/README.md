# Lightning Modules

This directory contains PyTorch Lightning modules for different tasks.

## Available Modules

### AudioFlowMatchingLitModule

**File**: `audio_flowmatching_module.py`

A Lightning module for audio generation using flow matching, specifically designed for text-to-music generation tasks.

#### Key Features

- **Flow Matching**: Uses ConditionalFlowMatcher for training with continuous normalizing flows
- **EMA Support**: Implements Exponential Moving Average for model weights
- **ODE Integration**: Uses torchdiffeq for inference with ODE solvers
- **No Metrics**: Follows audio_flow pattern of not using validation metrics
- **Validation**: Generates audio samples during validation for qualitative evaluation
- **Text2Music**: Designed for text-to-music generation with GTZAN dataset

#### Architecture

Based on the original `audio_flow/train.py` implementation:

1. **Training Step**:
   - Transform data to mel spectrograms and conditions
   - Sample noise and flow matching trajectory
   - Forward pass through BSRoformer model
   - Compute MSE loss between predicted and target velocity
   - Update EMA model

2. **Validation Step**:
   - Generate audio using ODE integration
   - Save audio files and spectrograms for qualitative evaluation
   - Use both main model and EMA model for comparison

3. **Inference**:
   - Use EMA model for best quality
   - ODE integration with configurable solver and steps

#### Configuration

**Lightning Module Config**: `configs/lit_module/audio_flowmatching.yaml`

Key parameters:
- `ema_decay`: EMA decay rate (default: 0.999)
- `validation_every_n_steps`: Validation frequency (default: 2000)
- `save_validation_outputs`: Whether to save validation audio (default: true)
- `validation_samples`: Number of samples to validate (default: 4)

#### Usage

```bash
# Train audio flow matching model
python src/train.py -cn train_audio_flowmatching

# Override parameters
python src/train.py -cn train_audio_flowmatching \
    lit_module.ema_decay=0.995 \
    lit_module.validation_every_n_steps=1000

# Use different data
python src/train.py -cn train_audio_flowmatching \
    data.batch_size=16 \
    data.clip_duration=10.0
```

#### Dependencies

- `torchcfm`: For ConditionalFlowMatcher
- `torchdiffeq`: For ODE integration
- `soundfile`: For audio I/O
- `matplotlib`: For spectrogram visualization

#### Model Requirements

Expects models dictionary with:
- `bsroformer`: Main BSRoformer model for flow matching
- `tokenizer`: Data transform module (e.g., Text2Music_Mel)

#### Differences from Original audio_flow

1. **Lightning Integration**: Full PyTorch Lightning module with hooks
2. **Hydra Configuration**: Uses Hydra for configuration management
3. **Modular Design**: Separates model, data transform, and training logic
4. **Enhanced Logging**: Integrated with Lightning loggers
5. **Validation Structure**: Organized validation with configurable frequency

### MNISTLitModule

**File**: `mnist_module.py`

Example Lightning module for MNIST classification (template/reference).

## Adding New Modules

1. Create new module file in `src/lit_module/`
2. Inherit from `LightningModule`
3. Implement required methods: `__init__`, `training_step`, `validation_step`, etc.
4. Create configuration file in `configs/lit_module/`
5. Add to training configuration defaults

## Best Practices

- Use `self.save_hyperparameters()` to save initialization parameters
- Implement proper metric tracking with `MetricCollection`
- Use `@rank_zero_only` for logging/saving operations in distributed training
- Handle empty metrics gracefully for tasks that don't need validation metrics
- Separate model logic from training logic for better modularity

"""Audio loading utilities using torchaudio.

This module provides a function to load audio files into PyTorch tensors, with support for offset,
duration, resampling, and mono conversion.
"""

from typing import Optional, Tuple

import torch
import torchaudio


def load_audio(
    path: str,
    sr: int,
    offset: float = 0.0,
    duration: float | None = None,
    mono: bool = False,
) -> tuple[torch.Tensor, int]:
    """Load audio using torchaudio only, returning a tensor and effective sample rate.

    Args:
        path: Path to audio file
        sr: Target sample rate
        offset: Start load time (seconds)
        duration: Duration to load (seconds), or None for full file
        mono: Return single-channel audio if True

    Returns:
        waveform: Tensor [C, T] in float32, normalized to [-1.0, 1.0]
        sr: The target sample rate
    """

    info = torchaudio.info(path)
    src_sr = info.sample_rate

    frame_offset = int(offset * src_sr)
    num_frames = int(duration * src_sr) if duration is not None else -1

    waveform, orig_sr = torchaudio.load(
        path,
        frame_offset=frame_offset,
        num_frames=num_frames,
    )  # waveform: Tensor [C, T], orig_sr == src_sr

    # Convert to float32 if needed and normalize to [-1,1]
    if waveform.dtype != torch.float32:
        max_val = waveform.abs().max()
        waveform = waveform.to(torch.float32)
        if max_val > 0:
            waveform = waveform / max_val

    # Resample if needed
    if orig_sr != sr:
        waveform = torchaudio.functional.resample(
            waveform, orig_freq=orig_sr, new_freq=sr
        )  # (https://docs.pytorch.org/audio/stable/generated/torchaudio.transforms.Resample.html)

    # Ensure exact duration
    if duration is not None:
        target_len = round(duration * sr)
        current_len = waveform.shape[-1]
        if current_len < target_len:
            pad = target_len - current_len
            waveform = torch.nn.functional.pad(waveform, (0, pad))
        elif current_len > target_len:
            waveform = waveform[..., :target_len]

    # Convert to mono if requested
    if mono and waveform.shape[0] > 1:
        waveform = waveform.mean(dim=0, keepdim=True)

    return waveform, sr

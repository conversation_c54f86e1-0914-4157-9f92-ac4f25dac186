"""Audio cropping utility for torch data pipelines.

Provides a unified cropper for both deterministic (fixed start) and random cropping, using a single
class and a control flag.
"""

import random
from typing import Optional, Tuple


class AudioCrop:
    """Audio cropper supporting both deterministic and random cropping.

    Args:
        clip_duration (float): Desired crop duration in seconds.
        random_crop (bool): If True, crop randomly; if False, crop from `start`.
        start (Optional[float]): Start time in seconds (only used if random_crop=False).
        end_pad (float): Amount of silent padding to add to the end, in seconds (only used
        if random_crop=True).
    """

    def __init__(
        self,
        clip_duration: float,
        random_crop: bool = False,
        start: float | None = 0.0,
        end_pad: float = 0.0,
    ):
        self.clip_duration = clip_duration
        self.random_crop = random_crop
        self.start = start
        self.end_pad = end_pad

    def __call__(self, audio_duration: float) -> tuple[float, float]:
        """
        Args:
            audio_duration (float): Total duration of the audio clip in seconds.

        Returns:
            (start_time, clip_duration): Tuple of start time and crop duration in seconds.
        """
        if self.random_crop:
            padded_duration = audio_duration + self.end_pad
            if self.clip_duration <= padded_duration:
                start_time = random.uniform(0.0, padded_duration - self.clip_duration)
            else:
                start_time = 0.0
            return start_time, self.clip_duration
        else:
            # Use fixed start point (could check bounds here if you want)
            return self.start if self.start is not None else 0.0, self.clip_duration

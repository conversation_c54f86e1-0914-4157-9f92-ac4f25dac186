"""DDP-aware InfiniteSampler for infinite, stateless, distributed training.

Randomly samples indices without replacement, infinite cycling and shuffling. Each DDP worker
(process) sees only its assigned portion of the indices. No explicit epoch, no external set_epoch,
and always ready for infinite loop training.

Subclass of torch.utils.data.Sampler for seamless PyTorch DataLoader integration.

Includes get_state and set_state with optional device transfer support.
"""

from collections.abc import Iterator, Sized
from typing import Any, Dict, Optional

import torch
from torch.utils.data import Sampler


class InfiniteSamplerDDP(Sampler[int]):
    """DDP-aware infinite sampler. Infinitely yields unique, randomly shuffled dataset indices
    assigned to this worker/process.

    Args:
        dataset (Sized): dataset or container to sample.
        seed (int, optional): random seed for reproducibility.
        num_replicas (int, optional): total DDP processes.
        rank (int, optional): DDP process index.
        drop_last (bool, optional): whether to drop remainder to make splits even.
    """

    def __init__(
        self,
        dataset: Sized,
        seed: int = 0,
        num_replicas: int | None = None,
        rank: int | None = None,
        drop_last: bool = False,
    ) -> None:
        super().__init__(data_source=dataset)
        self.dataset = dataset
        self.seed = seed
        self.drop_last = drop_last

        # DDP context
        if num_replicas is None:
            if torch.distributed.is_available() and torch.distributed.is_initialized():
                num_replicas = torch.distributed.get_world_size()
            else:
                num_replicas = 1
        if rank is None:
            if torch.distributed.is_available() and torch.distributed.is_initialized():
                rank = torch.distributed.get_rank()
            else:
                rank = 0
        self.num_replicas = num_replicas
        self.rank = rank

        self.dataset_len = len(self.dataset)
        if self.drop_last and self.dataset_len % self.num_replicas != 0:
            self.num_samples = self.dataset_len // self.num_replicas
        else:
            self.num_samples = (self.dataset_len + self.num_replicas - 1) // self.num_replicas
        self.total_size = self.num_samples * self.num_replicas

        self.generator = torch.Generator()
        self.p = 0  # position within my shard
        self.cycle_count = 0  # for reshuffling
        self.local_indices = []
        self._reshuffle()

    def _reshuffle(self):
        """Shuffle indices and assign non-overlapping chunk to each process."""
        g = self.generator
        g.manual_seed(self.seed + self.cycle_count)
        indices = list(range(self.dataset_len))
        perm = torch.randperm(len(indices), generator=g)
        indices = [indices[i] for i in perm.tolist()]

        # Pad or drop to ensure divisibility
        if not self.drop_last:
            padding = self.total_size - len(indices)
            if padding <= len(indices):
                indices += indices[:padding]
            else:
                indices += (indices * ((padding // len(indices)) + 1))[:padding]
        else:
            indices = indices[: self.total_size]

        # Each rank takes every num_replicas-th element (disjoint chunk)
        self.local_indices = indices[self.rank : self.total_size : self.num_replicas]
        self.p = 0
        self.cycle_count += 1

    def __iter__(self) -> Iterator[int]:
        """Infinite generator yielding only this rank's shuffled indices."""
        while True:
            if self.p == len(self.local_indices):
                self._reshuffle()
                self.p = 0
            yield self.local_indices[self.p]
            self.p += 1

    def __len__(self) -> int:
        return self.num_samples

    def get_state(self, device: torch.device | None = None) -> dict[str, Any]:
        """Get current state for checkpointing. Optionally move generator_state to a specific
        device.

        Args:
            device (torch.device, optional): Device to move generator_state tensors to.

        Returns:
            dict: Checkpoint state.
        """
        generator_state = self.generator.get_state()

        if device is not None:
            # Build a new dict with all tensors on the target device
            generator_state = {
                k: (v.to(device) if isinstance(v, torch.Tensor) else v)
                for k, v in generator_state.items()
            }
        return {
            "p": self.p,
            "cycle_count": self.cycle_count,
            "local_indices": list(self.local_indices),
            "generator_state": generator_state,
        }

    def set_state(self, state: dict[str, Any], device: torch.device | None = None) -> None:
        """Restore from checkpointed state. Optionally move generator_state to a specific device.

        Args:
            state (dict): State as returned by get_state.
            device (torch.device, optional): Device to move generator_state tensors to.
        """
        self.p = state["p"]
        self.cycle_count = state["cycle_count"]
        self.local_indices = list(state["local_indices"])
        generator_state = state["generator_state"]
        if device is not None:
            generator_state = {
                k: (v.to(device) if isinstance(v, torch.Tensor) else v)
                for k, v in generator_state.items()
            }
        self.generator.set_state(generator_state)

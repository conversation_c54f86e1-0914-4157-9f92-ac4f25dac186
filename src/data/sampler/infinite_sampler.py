"""infinite_sampler.py.

Provides an InfiniteSampler class for infinitely iterating over the indices of a sized dataset,
with reproducible shuffling using torch.Generator and an optional seed argument for full control
over randomness. Includes get_state/set_state for checkpointing and deterministic resumption.
"""

from collections.abc import Iterator, Sized
from typing import Any, Dict, Optional

import torch


class InfiniteSampler:
    """An infinite sampler for datasets supporting the Sized protocol.

    Randomly samples indices from the given dataset without replacement for each
    full traversal (epoch), using a torch.Generator for reproducible shuffling.
    A random seed can be set for deterministic behavior. After all indices have
    been yielded, the indices are reshuffled and sampling continues infinitely.
    Provides get_state and set_state for checkpointing and reproducibility.

    Args:
        dataset (Sized): The dataset (or any sized container) whose indices
            should be infinitely and randomly sampled.
        seed (int, optional): Random seed for reproducibility.
    """

    def __init__(self, dataset: Sized, seed: int | None = None) -> None:
        """Initializes the InfiniteSampler.

        Args:
            dataset (Sized): The dataset or sized container to sample from.
            seed (int, optional): Random seed for reproducibility.
        """
        self.indexes = list(range(len(dataset)))
        self.p = 0  # pointer
        self.generator = torch.Generator()
        if seed is not None:
            self.generator.manual_seed(seed)
        self._reshuffle()

    def _reshuffle(self):
        """Shuffle indexes in place using torch.Generator."""
        idx_tensor = torch.tensor(self.indexes)
        perm = torch.randperm(len(idx_tensor), generator=self.generator)
        self.indexes = idx_tensor[perm].tolist()

    def __iter__(self) -> Iterator[int]:
        """Infinite generator yielding randomly ordered indices of the dataset.

        Yields:
            int: The next randomly sampled index of the dataset.
        """
        while True:
            if self.p == len(self.indexes):
                self._reshuffle()
                self.p = 0

            index = self.indexes[self.p]
            self.p += 1

            yield index

    def __len__(self) -> int:
        """Returns the number of samples in one traversal of the dataset.

        Returns:
            int: The number of samples in the dataset.
        """
        return len(self.indexes)

    def get_state(self) -> dict[str, Any]:
        """Returns the current state of the sampler, including pointer, indexes, and
        torch.Generator state.

        Returns:
            dict: The state dictionary.
        """
        return {
            "p": self.p,
            "indexes": list(self.indexes),  # copy to avoid mutation
            "generator_state": self.generator.get_state(),
        }

    def set_state(self, state: dict[str, Any]) -> None:
        """Restores the sampler state from a state dictionary.

        Args:
            state (dict): State as returned by get_state.
        """
        self.p = state["p"]
        self.indexes = list(state["indexes"])
        self.generator.set_state(state["generator_state"])

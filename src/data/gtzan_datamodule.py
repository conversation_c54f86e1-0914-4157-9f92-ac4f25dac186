"""PyTorch Lightning DataModule for the GTZAN dataset.

A `LightningDataModule` implements 7 key methods:

```python
    def prepare_data(self):
    # Things to do on 1 GPU/TPU (not on every GPU/TPU in DDP).
    # Download data, pre-process, split, save to disk, etc...

    def setup(self, stage):
    # Things to do on every process in DDP.
    # Load data, set variables, etc...

    def train_dataloader(self):
    # return train dataloader

    def val_dataloader(self):
    # return validation dataloader

    def test_dataloader(self):
    # return test dataloader

    def predict_dataloader(self):
    # return predict dataloader

    def teardown(self, stage):
    # Called on every process in DDP.
    # Clean up after fit or test.
```

This allows you to share a full dataset without explaining how to download,
split, transform and process the data.

Example usage:
    from src.data.gtzan_datamodule import GTZANDataModule
    datamodule = GTZANDataModule(data_dir='path/to/gtzan', batch_size=32)
    datamodule.setup()
    train_loader = datamodule.train_dataloader()
"""

import os
from collections.abc import Callable
from pathlib import Path
from typing import Any, Optional

import torch
from lightning import LightningDataModule
from torch.utils.data import DataLoader

from src.data.components.gtzan import GTZAN
from src.data.components.io.crop import AudioCrop
from src.data.components.transforms.common import ToMono
from src.data.components.transforms.onehot import OneHot
from src.data.sampler.infinite_sampler import InfiniteSampler
from src.data.sampler.infinite_sampler_ddp import InfiniteSamplerDDP


class GTZANDataModule(LightningDataModule):
    """PyTorch Lightning DataModule for GTZAN music genre classification.

    GTZAN dataset contains 1,000 30-second audio clips across 10 music genres. This DataModule
    handles train/val/test splits using 10-fold cross-validation.
    """

    def __init__(
        self,
        data_dir: str,
        batch_size: int = 32,
        num_workers: int = 4,
        sr: int = 22050,
        test_fold: int = 0,
        val_fold: int = 1,
        clip_duration: float = 5.0,
        crop: Callable | None = None,
        train_transform: Callable | None = None,
        val_transform: Callable | None = None,
        test_transform: Callable | None = None,
        target_transform: Callable | None = None,
        pin_memory: bool = True,
        drop_last: bool = False,
        use_infinite_sampler: bool = False,
        sampler_seed: int = 42,
    ) -> None:
        """Initialize GTZANDataModule.

        Args:
            data_dir: Path to GTZAN dataset directory.
            batch_size: Batch size for dataloaders.
            num_workers: Number of workers for data loading.
            sr: Audio sampling rate.
            test_fold: Which fold to use for testing (0-9).
            val_fold: Which fold to use for validation (0-9).
            clip_duration: Duration of audio clips in seconds.
            crop: Callable for cropping audio (returns start, duration).
            train_transform: Transform for training data.
            val_transform: Transform for validation data.
            test_transform: Transform for test data.
            target_transform: Transform for targets/labels.
            pin_memory: Whether to pin memory for faster GPU transfer.
            drop_last: Whether to drop last incomplete batch.
            use_infinite_sampler: Whether to use infinite sampler for training.
            sampler_seed: Random seed for infinite sampler.
        """
        super().__init__()

        # Store number of classes
        self.num_classes = GTZAN.CLASSES_NUM

        # This line allows to access init params with 'self.hparams' attribute
        # Also ensures init params will be stored in checkpoint
        self.save_hyperparameters(logger=False)

        # Store parameters as instance attributes for compatibility
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.sr = sr
        self.test_fold = test_fold
        self.val_fold = val_fold
        self.clip_duration = clip_duration
        self.crop = crop
        self.train_transform = train_transform
        self.val_transform = val_transform
        self.test_transform = test_transform
        self.target_transform = target_transform
        self.pin_memory = pin_memory
        self.drop_last = drop_last
        self.use_infinite_sampler = use_infinite_sampler
        self.sampler_seed = sampler_seed

        # Initialize datasets as None
        self.data_train: GTZAN | None = None
        self.data_val: GTZAN | None = None
        self.data_test: GTZAN | None = None

        # Initialize batch size per device
        self.batch_size_per_device = batch_size

    def prepare_data(self) -> None:
        """Download data if needed.

        Lightning ensures that `self.prepare_data()` is called only within a single process on CPU,
        so you can safely add your downloading logic within. In case of multi-node training, the
        execution of this hook depends upon `self.prepare_data_per_node()`.

        Do not use it to assign state (self.x = y).
        """
        # GTZAN dataset is typically pre-downloaded, but we can validate it exists

        genres_dir = Path(self.data_dir) / "genres"
        if not genres_dir.exists():
            raise RuntimeError(
                f"GTZAN dataset not found at {genres_dir}. "
                f"Please download GTZAN dataset to {self.data_dir}"
            )

    def setup(self, stage: str | None = None) -> None:
        """Load data and set up datasets.

        This method is called by Lightning before `trainer.fit()`, `trainer.validate()`,
        `trainer.test()`, and `trainer.predict()`, so be careful not to execute things
        like random split twice!

        Args:
            stage: The stage to setup. Either "fit", "validate", "test", or "predict".
        """
        # Divide batch size by the number of devices
        if self.trainer is not None:
            if self.batch_size % self.trainer.world_size != 0:
                raise RuntimeError(
                    f"Batch size ({self.batch_size}) is not divisible by the number of "
                    f"devices ({self.trainer.world_size})."
                )
            self.batch_size_per_device = self.batch_size // self.trainer.world_size

        # Load datasets only if not loaded already
        if not self.data_train and not self.data_val and not self.data_test:
            # Set up crops and transforms
            train_crop = (
                self.crop
                if self.crop is not None
                else AudioCrop(clip_duration=self.clip_duration, random_crop=True)
            )
            test_crop = (
                self.crop
                if self.crop is not None
                else AudioCrop(clip_duration=self.clip_duration, random_crop=False)
            )

            # Set up transforms with defaults
            train_transform = (
                self.train_transform if self.train_transform is not None else ToMono()
            )
            val_transform = self.val_transform if self.val_transform is not None else ToMono()
            test_transform = self.test_transform if self.test_transform is not None else ToMono()
            target_transform = (
                self.target_transform
                if self.target_transform is not None
                else OneHot(num_classes=GTZAN.CLASSES_NUM)
            )

            # Training dataset
            self.data_train = GTZAN(
                data_dir=self.data_dir,
                split="train",
                test_fold=self.test_fold,
                sr=self.sr,
                crop=train_crop,
                clip_duration=self.clip_duration,
                transform=train_transform,
                target_transform=target_transform,
            )

            # Validation dataset (using different fold)
            self.data_val = GTZAN(
                data_dir=self.data_dir,
                split="test",  # Use test split but different fold
                test_fold=self.val_fold,
                sr=self.sr,
                crop=test_crop,
                clip_duration=self.clip_duration,
                transform=val_transform,
                target_transform=target_transform,
            )

            # Test dataset
            self.data_test = GTZAN(
                data_dir=self.data_dir,
                split="test",
                test_fold=self.test_fold,
                sr=self.sr,
                crop=test_crop,
                clip_duration=self.clip_duration,
                transform=test_transform,
                target_transform=target_transform,
            )

    def train_dataloader(self) -> DataLoader[Any]:
        """Create and return the train dataloader.

        Returns:
            The train dataloader.
        """
        if self.use_infinite_sampler:
            # Determine if we're in a distributed setting
            is_distributed = self.trainer is not None and self.trainer.world_size > 1

            if is_distributed:
                # Use DDP-aware infinite sampler
                sampler = InfiniteSamplerDDP(
                    dataset=self.data_train,
                    seed=self.sampler_seed,
                    drop_last=self.drop_last,
                )
            else:
                # Use regular infinite sampler
                sampler = InfiniteSampler(
                    dataset=self.data_train,
                    seed=self.sampler_seed,
                )

            return DataLoader(
                dataset=self.data_train,
                batch_size=self.batch_size_per_device,
                sampler=sampler,
                num_workers=self.num_workers,
                pin_memory=self.pin_memory,
                drop_last=False,  # Let sampler handle this
                collate_fn=self._default_collate,
            )

        # Use default PyTorch DataLoader with shuffle
        return DataLoader(
            dataset=self.data_train,
            batch_size=self.batch_size_per_device,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            drop_last=self.drop_last,
            collate_fn=self._default_collate,
        )

    def val_dataloader(self) -> DataLoader[Any]:
        """Create and return the validation dataloader.

        Returns:
            The validation dataloader.
        """
        return DataLoader(
            dataset=self.data_val,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            drop_last=False,
            collate_fn=self._default_collate,
        )

    def test_dataloader(self) -> DataLoader[Any]:
        """Create and return the test dataloader.

        Returns:
            The test dataloader.
        """
        return DataLoader(
            dataset=self.data_test,
            batch_size=self.batch_size_per_device,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
            drop_last=False,
            collate_fn=self._default_collate,
        )

    @staticmethod
    def _default_collate(batch: list) -> dict[str, Any]:
        """Custom collate function for handling GTZAN batch data.

        Args:
            batch: List of dataset items.

        Returns:
            Collated batch as dictionary.
        """
        out = {}
        for key in batch[0]:
            values = [item[key] for item in batch]
            if isinstance(values[0], torch.Tensor):
                out[key] = torch.stack(values)
            else:
                out[key] = values
        return out

    def teardown(self, stage: str | None = None) -> None:
        """Lightning hook for cleaning up after `trainer.fit()`, `trainer.validate()`,
        `trainer.test()`, and `trainer.predict()`.

        Args:
            stage: The stage being torn down. Either "fit", "validate", "test", or "predict".
        """
        self.data_train = None
        self.data_val = None
        self.data_test = None

    def state_dict(self) -> dict[str, Any]:
        """Called when saving a checkpoint. Implement to generate and save the datamodule state.

        Returns:
            A dictionary containing the datamodule state that you want to save.
        """
        return {
            "data_dir": self.data_dir,
            "batch_size": self.batch_size,
            "num_workers": self.num_workers,
            "sr": self.sr,
            "test_fold": self.test_fold,
            "val_fold": self.val_fold,
            "clip_duration": self.clip_duration,
            "use_infinite_sampler": self.use_infinite_sampler,
            "sampler_seed": self.sampler_seed,
        }

    def load_state_dict(self, state_dict: dict[str, Any]) -> None:
        """Called when loading a checkpoint. Implement to reload datamodule state.

        Args:
            state_dict: The datamodule state returned by `self.state_dict()`.
        """
        # Update instance attributes with state_dict values
        for key, value in state_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)


def _quick_test(data_dir: str) -> None:
    """Quick test function for GTZANDataModule."""
    print(f"Testing GTZANDataModule on data_dir: {data_dir}")

    # Test basic functionality
    print("\n=== Testing without infinite sampler ===")
    datamodule = GTZANDataModule(data_dir=data_dir, batch_size=2, num_workers=0)
    datamodule.prepare_data()
    datamodule.setup()

    # Test train dataloader
    train_loader = datamodule.train_dataloader()
    train_batch = next(iter(train_loader))
    print(f"Train batch keys: {list(train_batch.keys())}")
    print(f"Train audio shape: {train_batch['audio'].shape}")
    print(f"Train labels: {train_batch['label']}")

    # Test val dataloader
    val_loader = datamodule.val_dataloader()
    val_batch = next(iter(val_loader))
    print(f"Val batch keys: {list(val_batch.keys())}")
    print(f"Val audio shape: {val_batch['audio'].shape}")

    # Test test dataloader
    test_loader = datamodule.test_dataloader()
    test_batch = next(iter(test_loader))
    print(f"Test batch keys: {list(test_batch.keys())}")
    print(f"Test audio shape: {test_batch['audio'].shape}")

    print(f"Number of classes: {datamodule.num_classes}")

    # Test with infinite sampler
    print("\n=== Testing with infinite sampler ===")
    datamodule_inf = GTZANDataModule(
        data_dir=data_dir, batch_size=2, num_workers=0, use_infinite_sampler=True, sampler_seed=123
    )
    datamodule_inf.prepare_data()
    datamodule_inf.setup()

    train_loader_inf = datamodule_inf.train_dataloader()
    train_batch_inf = next(iter(train_loader_inf))
    print(f"Infinite sampler train batch keys: {list(train_batch_inf.keys())}")
    print(f"Infinite sampler train audio shape: {train_batch_inf['audio'].shape}")

    print("Test passed!")


if __name__ == "__main__":
    _GTZAN_ROOT = "/home/<USER>/Workspace/datasets/gtzan"

    if not os.path.exists(_GTZAN_ROOT):
        print("Please set _GTZAN_ROOT to a valid GTZAN dataset directory.")
        print(f"Current path: {_GTZAN_ROOT}")
    else:
        _quick_test(_GTZAN_ROOT)

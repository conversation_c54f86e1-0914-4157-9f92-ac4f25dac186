"""Exponential Moving Average (EMA) utilities for model training.

This module provides utilities for maintaining exponential moving averages of model parameters,
which is commonly used in generative models and flow matching to improve model stability and
generation quality.

The EMA technique maintains a shadow copy of the model parameters that are updated using:
    ema_param = decay * ema_param + (1 - decay) * current_param

This helps stabilize training and often produces better results during inference.
"""

from copy import deepcopy
from typing import Optional

import torch
from torch import nn


class EMA:
    """Exponential Moving Average for PyTorch models.

    This class maintains an exponential moving average of model parameters and buffers.
    It's particularly useful for generative models where EMA weights often produce
    better quality outputs during inference.

    Example:
        >>> model = MyModel()
        >>> ema = EMA(model, decay=0.999)
        >>>
        >>> # During training
        >>> for batch in dataloader:
        >>>     loss = train_step(model, batch)
        >>>     optimizer.step()
        >>>     ema.update(model)  # Update EMA weights
        >>>
        >>> # During inference
        >>> with ema.average_parameters():
        >>>     output = model(input)  # Uses EMA weights
    """

    def __init__(
        self,
        model: nn.Module,
        decay: float = 0.999,
        update_buffers: bool = True,
        device: Optional[torch.device] = None
    ):
        """Initialize EMA.

        Args:
            model: The model to track with EMA
            decay: EMA decay rate. Higher values (closer to 1) mean slower updates
            update_buffers: Whether to apply EMA to model buffers (e.g., BatchNorm stats)
            device: Device to store EMA parameters. If None, uses model's device
        """
        self.decay = decay
        self.update_buffers = update_buffers

        # Create a deep copy of the model for EMA
        self.ema_model = deepcopy(model)
        self.ema_model.eval()  # EMA model should always be in eval mode

        # Disable gradients for EMA model
        for param in self.ema_model.parameters():
            param.requires_grad_(False)

        # Move to specified device if provided
        if device is not None:
            self.ema_model.to(device)

        # Initialize EMA with current model weights (decay=0 for first update)
        self.update(model, decay=0.0)

    @torch.no_grad()
    def update(self, model: nn.Module, decay: Optional[float] = None) -> None:
        """Update EMA parameters with current model parameters.

        Args:
            model: Current model to update EMA from
            decay: Override decay rate for this update. If None, uses self.decay
        """
        if decay is None:
            decay = self.decay

        # Update parameters
        ema_params = dict(self.ema_model.named_parameters())
        model_params = dict(model.named_parameters())

        for name, param in model_params.items():
            if name in ema_params:
                ema_params[name].mul_(decay).add_(param.data, alpha=1 - decay)

        # Update buffers (e.g., BatchNorm running stats)
        if self.update_buffers:
            ema_buffers = dict(self.ema_model.named_buffers())
            model_buffers = dict(model.named_buffers())

            for name, buffer in model_buffers.items():
                if name in ema_buffers:
                    # Skip integer buffers (e.g., num_batches_tracked in BatchNorm)
                    if buffer.dtype.is_floating_point:
                        ema_buffers[name].mul_(decay).add_(buffer.data, alpha=1 - decay)

    def get_model(self) -> nn.Module:
        """Get the EMA model for inference.

        Returns:
            The EMA model with averaged parameters
        """
        return self.ema_model

    def state_dict(self) -> dict:
        """Get EMA state dict for checkpointing.

        Returns:
            Dictionary containing EMA model state and decay rate
        """
        return {
            'ema_model': self.ema_model.state_dict(),
            'decay': self.decay,
            'update_buffers': self.update_buffers,
        }

    def load_state_dict(self, state_dict: dict) -> None:
        """Load EMA state from checkpoint.

        Args:
            state_dict: Dictionary containing EMA state
        """
        self.ema_model.load_state_dict(state_dict['ema_model'])
        self.decay = state_dict.get('decay', self.decay)
        self.update_buffers = state_dict.get('update_buffers', self.update_buffers)

    def copy_to(self, model: nn.Module) -> None:
        """Copy EMA parameters to another model.

        Args:
            model: Target model to copy EMA parameters to
        """
        with torch.no_grad():
            ema_params = dict(self.ema_model.named_parameters())
            model_params = dict(model.named_parameters())

            for name, param in model_params.items():
                if name in ema_params:
                    param.copy_(ema_params[name])

            if self.update_buffers:
                ema_buffers = dict(self.ema_model.named_buffers())
                model_buffers = dict(model.named_buffers())

                for name, buffer in model_buffers.items():
                    if name in ema_buffers and buffer.dtype.is_floating_point:
                        buffer.copy_(ema_buffers[name])


@torch.no_grad()
def update_ema(ema_model: nn.Module, model: nn.Module, decay: float = 0.999) -> None:
    """Simple function to update EMA model parameters.

    This is a simplified version for backward compatibility and cases where
    you don't need the full EMA class functionality.

    Args:
        ema_model: EMA model to update
        model: Current model to update from
        decay: EMA decay rate
    """
    # Update parameters
    ema_params = dict(ema_model.named_parameters())
    model_params = dict(model.named_parameters())

    for name, param in model_params.items():
        if name in ema_params:
            ema_params[name].mul_(decay).add_(param.data, alpha=1 - decay)

    # Update buffers (e.g., BatchNorm running stats)
    ema_buffers = dict(ema_model.named_buffers())
    model_buffers = dict(model.named_buffers())

    for name, buffer in model_buffers.items():
        if name in ema_buffers:
            # Skip integer buffers (e.g., num_batches_tracked in BatchNorm)
            if buffer.dtype.is_floating_point:
                ema_buffers[name].mul_(decay).add_(buffer.data, alpha=1 - decay)
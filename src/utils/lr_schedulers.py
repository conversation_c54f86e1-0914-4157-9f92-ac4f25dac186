"""Learning rate schedulers for training."""

from typing import Callable


class LinearWarmupLR:
    """Linear warmup learning rate scheduler.
    
    This scheduler linearly increases the learning rate from 0 to the base learning rate
    over the specified number of warmup steps, then maintains the base learning rate.
    
    Based on the LinearWarmUp class from audio_flow/utils.py.
    """
    
    def __init__(self, warmup_steps: int):
        """Initialize the linear warmup scheduler.
        
        :param warmup_steps: Number of steps for linear warmup
        """
        self.warmup_steps = warmup_steps
    
    def __call__(self, step: int) -> float:
        """Compute the learning rate multiplier for the given step.
        
        :param step: Current training step (0-indexed)
        :return: Learning rate multiplier
        """
        if step < self.warmup_steps:
            return float(step) / float(max(1, self.warmup_steps))
        else:
            return 1.0


class CosineAnnealingWarmupLR:
    """Cosine annealing with linear warmup learning rate scheduler.
    
    This scheduler combines linear warmup with cosine annealing decay.
    """
    
    def __init__(self, warmup_steps: int, total_steps: int, min_lr_ratio: float = 0.0):
        """Initialize the cosine annealing warmup scheduler.
        
        :param warmup_steps: Number of steps for linear warmup
        :param total_steps: Total number of training steps
        :param min_lr_ratio: Minimum learning rate as a ratio of base learning rate
        """
        self.warmup_steps = warmup_steps
        self.total_steps = total_steps
        self.min_lr_ratio = min_lr_ratio
    
    def __call__(self, step: int) -> float:
        """Compute the learning rate multiplier for the given step.
        
        :param step: Current training step (0-indexed)
        :return: Learning rate multiplier
        """
        import math
        
        if step < self.warmup_steps:
            # Linear warmup
            return float(step) / float(max(1, self.warmup_steps))
        else:
            # Cosine annealing
            progress = (step - self.warmup_steps) / (self.total_steps - self.warmup_steps)
            progress = min(progress, 1.0)  # Clamp to [0, 1]
            
            cosine_factor = 0.5 * (1 + math.cos(math.pi * progress))
            return self.min_lr_ratio + (1 - self.min_lr_ratio) * cosine_factor


def get_linear_warmup_lr(warmup_steps: int) -> Callable[[int], float]:
    """Get a linear warmup learning rate function.
    
    :param warmup_steps: Number of steps for linear warmup
    :return: Learning rate function
    """
    return LinearWarmupLR(warmup_steps)


def get_cosine_annealing_warmup_lr(
    warmup_steps: int, 
    total_steps: int, 
    min_lr_ratio: float = 0.0
) -> Callable[[int], float]:
    """Get a cosine annealing with warmup learning rate function.
    
    :param warmup_steps: Number of steps for linear warmup
    :param total_steps: Total number of training steps
    :param min_lr_ratio: Minimum learning rate as a ratio of base learning rate
    :return: Learning rate function
    """
    return CosineAnnealingWarmupLR(warmup_steps, total_steps, min_lr_ratio)

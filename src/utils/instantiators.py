from typing import Dict, List

import hydra
from lightning import Callback
from lightning.pytorch.loggers import Logger
from omegaconf import DictConfig
from torch import nn

from src.utils import pylogger

log = pylogger.RankedLogger(__name__, rank_zero_only=True)


def instantiate_callbacks(callbacks_cfg: DictConfig) -> list[Callback]:
    """Instantiates callbacks from config.

    :param callbacks_cfg: A DictConfig object containing callback configurations.
    :return: A list of instantiated callbacks.
    """
    callbacks: list[Callback] = []

    if not callbacks_cfg:
        log.warning("No callback configs found! Skipping..")
        return callbacks

    if not isinstance(callbacks_cfg, DictConfig):
        raise TypeError("Callbacks config must be a DictConfig!")

    for _, cb_conf in callbacks_cfg.items():
        if isinstance(cb_conf, DictConfig) and "_target_" in cb_conf:
            log.info(f"Instantiating callback <{cb_conf._target_}>")
            callbacks.append(hydra.utils.instantiate(cb_conf))

    return callbacks


def instantiate_loggers(logger_cfg: DictConfig) -> list[Logger]:
    """Instantiates loggers from config.

    :param logger_cfg: A DictConfig object containing logger configurations.
    :return: A list of instantiated loggers.
    """
    logger: list[Logger] = []

    if not logger_cfg:
        log.warning("No logger configs found! Skipping...")
        return logger

    if not isinstance(logger_cfg, DictConfig):
        raise TypeError("Logger config must be a DictConfig!")

    for _, lg_conf in logger_cfg.items():
        if isinstance(lg_conf, DictConfig) and "_target_" in lg_conf:
            log.info(f"Instantiating logger <{lg_conf._target_}>")
            logger.append(hydra.utils.instantiate(lg_conf))

    return logger


def instantiate_models(models_cfg: DictConfig) -> list[nn.Module]:
    """Instantiates models from config.

    :param models_cfg: A DictConfig object containing model configurations.
    :return: A list of instantiated models.
    """
    models: dict[str, nn.Module] = {}

    if not models_cfg:
        log.warning("No model configs found! Skipping...")
        return models

    if not isinstance(models_cfg, DictConfig):
        raise TypeError("Models config must be a DictConfig!")

    for model_name, model_conf in models_cfg.items():
        if isinstance(model_conf, DictConfig) and "_target_" in model_conf:
            log.info(f"Instantiating model <{model_conf._target_}>")
            models[model_name] = hydra.utils.instantiate(model_conf)

    return models


def instantiate_losses(losses_cfg: DictConfig) -> list[nn.Module]:
    """Instantiates losses from config.

    :param losses_cfg: A DictConfig object containing loss configurations.
    :return: A list of instantiated losses.
    """
    losses: dict[dict[str, nn.Module]] = {}
    loss_fn: dict[str, nn.Module] = {}
    beta: dict[str, float] = {}

    if not losses_cfg:
        log.warning("No loss configs found! Skipping...")
        return losses

    if not isinstance(losses_cfg, DictConfig):
        raise TypeError("Losses config must be a DictConfig!")

    for (loss_name, loss_conf), (beta_name, beta_value) in zip(
        losses_cfg.loss_fn.items(), losses_cfg.beta.items()
    ):
        if isinstance(loss_conf, DictConfig) and "_target_" in loss_conf:
            log.info(f"Instantiating loss <{loss_conf._target_}>")
            loss_fn[loss_name] = hydra.utils.instantiate(loss_conf)
            beta[beta_name] = beta_value

    losses["loss_fn"] = loss_fn
    losses["beta"] = beta
    return losses


def instantiate_metrics(metrics_cfg: DictConfig) -> dict[str, nn.Module]:
    """Instantiates metrics from config.

    :param metrics_cfg: A DictConfig object containing metric configurations.
    :return: A dict of instantiated metrics.
    """
    metrics: dict[str, nn.Module] = {}

    if not metrics_cfg:
        log.warning("No metric configs found! Skipping...")
        return metrics

    if not isinstance(metrics_cfg, DictConfig):
        raise TypeError("Metrics config must be a DictConfig!")

    # Check if metric_fn key exists
    if not hasattr(metrics_cfg, 'metric_fn') or metrics_cfg.metric_fn is None:
        log.warning("No 'metric_fn' key found in metrics config! Skipping...")
        return metrics

    for metric_name, metric_conf in metrics_cfg.metric_fn.items():
        if isinstance(metric_conf, DictConfig) and "_target_" in metric_conf:
            log.info(f"Instantiating metric <{metric_conf._target_}>")
            metrics[metric_name] = hydra.utils.instantiate(metric_conf)

    return metrics

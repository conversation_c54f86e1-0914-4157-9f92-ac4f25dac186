import os
import shutil
import sys
import warnings
from collections.abc import Callable
from contextlib import contextmanager
from importlib.util import find_spec
from pathlib import Path
from typing import Any, Optional

from lightning_utilities.core.rank_zero import rank_zero_only
from omegaconf import DictConfig

from src.utils import pylogger, rich_utils

log = pylogger.RankedLogger(__name__, rank_zero_only=True)


def extras(cfg: DictConfig) -> None:
    """Applies optional utilities before the task is started.

    Utilities:
        - Ignoring python warnings
        - Setting tags from command line
        - Rich config printing

    :param cfg: A DictConfig object containing the config tree.
    """
    # return if no `extras` config
    if not cfg.get("extras"):
        log.warning("Extras config not found! <cfg.extras=null>")
        return

    # disable python warnings
    if cfg.extras.get("ignore_warnings"):
        log.info("Disabling python warnings! <cfg.extras.ignore_warnings=True>")
        warnings.filterwarnings("ignore")

    # copy configs, src code, and tests to hydra runtime output_dir
    if cfg.extras.get("copy_configs_src_and_tests"):
        log.info(
            "copy configs, src code, and tests to output_dir! "
            "<cfg.extras.copy_configs_src_and_tests=True>"
        )
        copy_configs_src_and_tests(cfg)

    # prompt user to input tags from command line if none are provided in the config
    if cfg.extras.get("enforce_tags"):
        log.info("Enforcing tags! <cfg.extras.enforce_tags=True>")
        rich_utils.enforce_tags(cfg, save_to_file=True)

    # pretty print config tree using Rich library
    if cfg.extras.get("print_config"):
        log.info("Printing config tree with Rich! <cfg.extras.print_config=True>")
        rich_utils.print_config_tree(cfg, resolve=True, save_to_file=True)


@rank_zero_only
def copy_configs_src_and_tests(cfg: DictConfig) -> None:
    """Copy configs, src codes, and tests codes from hydra work_dir to output_dir / "codes".

    Args:
        cfg (DictConfig): A DictConfig composed by Hydra.
    """
    work_dir = Path(cfg.paths.work_dir)
    configs_dir = work_dir / "configs"
    src_dir = work_dir / "src"
    tests_dir = work_dir / "tests"
    output_dir = Path(cfg.paths.output_dir) / "codes"
    copy_folder(src=configs_dir, dst=output_dir)
    copy_folder(src=src_dir, dst=output_dir)
    copy_folder(src=tests_dir, dst=output_dir)


def copy_folder(
    src: Path, dst: Path, overwrite: bool = False, preserve_metadata: bool = True
) -> None:
    """Copies the source folder to the destination folder, including all files and subdirectories.

    Args:
        src (Path): The source directory to copy.
        dst (Path): The destination directory where the source will be copied.
        overwrite (bool, optional): Whether to overwrite the destination if it exists.
                                    Defaults to False.
        preserve_metadata (bool, optional): Whether to preserve file metadata (e.g., timestamps).
                                            Defaults to True.

    Returns:
    - None
    """
    try:
        # Ensure source directory exists and is a directory
        if not src.exists():
            log.error(f"Source directory '{src}' does not exist.")
            return
        if not src.is_dir():
            log.error(f"Source '{src}' is not a directory.")
            return

        # Construct the full destination path
        dst_path = dst / src.name

        # Handle overwriting the destination folder
        if dst_path.exists():
            if overwrite:
                log.warning(f"Destination folder '{dst_path}' exists. Overwriting it.")
                shutil.rmtree(dst_path)  # Remove the existing destination folder
            else:
                log.error(
                    f"Destination folder '{dst_path}' already exists. "
                    "Use overwrite=True to replace it."
                )
                return

        # Ignore Python temporary files and folders
        ignore = shutil.ignore_patterns("__pycache__", "*.pyc", "tmp*", "*.tmp")

        # Copy the directory tree
        copy_function = shutil.copy2 if preserve_metadata else shutil.copy
        shutil.copytree(
            src,
            dst_path,
            ignore=ignore,
            dirs_exist_ok=False,
            copy_function=copy_function,
        )

        log.info(f"Successfully copied '{src}' to '{dst_path}'.")

    except (FileNotFoundError, PermissionError, shutil.Error) as e:
        log.exception(f"An error occurred while copying the folder: {e}")


def task_wrapper(task_func: Callable) -> Callable:
    """Optional decorator that controls the failure behavior when executing the task function.

    This wrapper can be used to:
        - make sure loggers are closed even if the task function raises an exception
            (prevents multirun failure)
        - save the exception to a `.log` file
        - mark the run as failed with a dedicated file in the `logs/` folder
            (so we can find and rerun it later)
        - etc. (adjust depending on your needs)

    Example:
    ```
    @utils.task_wrapper
    def train(cfg: DictConfig) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        ...
        return metric_dict, object_dict
    ```

    :param task_func: The task function to be wrapped.

    :return: The wrapped task function.
    """

    def wrap(cfg: DictConfig) -> tuple[dict[str, Any], dict[str, Any]]:
        # execute the task
        try:
            metric_dict, object_dict = task_func(cfg=cfg)

        # things to do if exception occurs
        except Exception as ex:
            # save exception to `.log` file
            log.exception("")

            # some hyperparameter combinations might be invalid or cause out-of-memory errors
            # so when using hparam search plugins like Optuna, you might want to disable
            # raising the below exception to avoid multirun failure
            raise ex

        # things to always do after either success or exception
        finally:
            # display output dir path in terminal
            log.info(f"Output dir: {cfg.paths.output_dir}")

            # always close wandb run (even if exception occurs so multirun won't fail)
            if find_spec("wandb"):  # check if wandb is installed
                import wandb

                if wandb.run:
                    log.info("Closing wandb!")
                    wandb.finish()

        return metric_dict, object_dict

    return wrap


def get_metric_value(metric_dict: dict[str, Any], metric_name: str | None) -> float | None:
    """Safely retrieves value of the metric logged in LightningModule.

    :param metric_dict: A dict containing metric values.
    :param metric_name: If provided, the name of the metric to retrieve.
    :return: If a metric name was provided, the value of the metric.
    """
    if not metric_name:
        log.info("Metric name is None! Skipping metric value retrieval...")
        return None

    if metric_name not in metric_dict:
        raise KeyError(
            f"Metric value not found! <metric_name={metric_name}>\n"
            "Make sure metric name logged in LightningModule is correct!\n"
            "Make sure `optimized_metric` name in `hparams_search` config is correct!"
        )

    metric_value = metric_dict[metric_name].item()
    log.info(f"Retrieved metric value! <{metric_name}={metric_value}>")

    return metric_value


@contextmanager
def suppress_print():
    original_stdout = sys.stdout
    sys.stdout = open(os.devnull, "w")
    try:
        yield
    finally:
        sys.stdout.close()
        sys.stdout = original_stdout
